import { GetJWT } from "./auth.js";
import { GetUserID } from "./myQuery.js";
import { GetUserInfo } from "./myQuery.js";
import { GetAuditStatus } from "./myQuery.js";
import {GetEventId} from "./myQuery.js";
import {GetUsersXP} from "./myQuery.js";
import {GetProjectXPMapping} from "./myQuery.js";
import { GetProjectsStatus } from "./myQuery.js";
import {GetCohortUsers} from "./myQuery.js";
import {UsersByXP} from "./myQuery.js";
import {getUserXpAndLevel} from "./myQuery.js";
import {getUserSkills} from "./myQuery.js";

//**************************** home page ****************************/

// Function to render the login page
function renderLoginPage() {
  const app = document.getElementById("app");
  app.innerHTML = `
    <div id="loginPage">
      <form id="loginForm">
      <h2>Login</h2>

        <label for="usernameOrEmail">Username or Email:</label>
        <input type="text" id="usernameOrEmail" name="usernameOrEmail" required placeholder="username or email">

        <br>
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required placeholder="password">
        <br>
        <button type="submit" id="loginButton">Login</button>
      </form>
    </div>
  `;

  const loginForm = document.getElementById("loginForm");
  loginForm.addEventListener("submit", async (event) => {
    event.preventDefault(); // Prevent form submission

    // Get the username/email and password from the form
    const usernameOrEmail = event.target.usernameOrEmail.value;
    const password = event.target.password.value;

    try {
      const temp_jwt = await GetJWT(usernameOrEmail, password);
      sessionStorage.setItem("jwt", temp_jwt);

      renderChartsPage(temp_jwt);
    } catch (error) {
      console.error("Error obtaining JWT:", error.message);
    }
  });
}

//*********************************************************************/
//**************************** Chart page ****************************/

// Function to render the charts page
async function renderChartsPage(jwt) {
  // Call UserInfo function to get user info
  const { userID, userName } = await GetUserID(jwt);
  // call GetUserInfo function to get user info
  const { userInfo } = await GetUserInfo(userName, jwt);
  const { passCount, failCount, nullCount}= await GetAuditStatus(jwt);
  const { eventId }= await GetEventId(jwt,userName);
  const { usersXP } = await GetUsersXP(jwt);
  const { projectXPMapping } = await GetProjectXPMapping(eventId, jwt);
  const { statusCounts, projects } = await GetProjectsStatus(userName,eventId,usersXP,jwt);
  const { users } = await GetCohortUsers(jwt,eventId);
  const { xp, level } = await getUserXpAndLevel(userName, eventId, jwt);
  const skills = await getUserSkills(userID, jwt)
  // console.log("cohortUsers:", users);

  // Check if cohortUsers is not empty
  if (!users || users.length === 0) {
    throw new Error("Cohort users are undefined or empty.");
  }

  // Fetch users by XP using comprehensive project XP mapping for accurate ranking
  const { usersByXP } = await UsersByXP(jwt, eventId, users, projectXPMapping);
  // console.log(usersByXP);


  const app = document.getElementById("app");
  app.innerHTML = `
  <div id="welcome">
    <div>
      <p>Welcome, ${userName}</p>
      <div class="welcome-stats">
        <span class="stat-item">ID: ${userID}</span>
        <span class="stat-item">XP: ${(xp/1000000).toFixed(2)} MB</span>
        <span class="stat-item">Level: ${level}</span>
        <span class="stat-item">Event: ${eventId}</span>
      </div>
    </div>
    <button id="logoutButton" class="btn">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
        <polyline points="16,17 21,12 16,7"></polyline>
        <line x1="21" y1="12" x2="9" y2="12"></line>
      </svg>
      Logout
    </button>
  </div>

  <div id="chartsPage">
    <div id="leftSide">
      <div id="userInfo">
        <p>👤 User Information</p>
        <div><span>First Name</span><span>${userInfo.firstName}</span></div>
        <div><span>Last Name</span><span>${userInfo.lastName}</span></div>
        <div><span>Email</span><span>${userInfo.email}</span></div>
        <div><span>Campus</span><span>${userInfo.campus}</span></div>
        <div><span>Joined</span><span>${formatDate(userInfo.createdAt)}</span></div>
        <div><span>Last Update</span><span>${formatDate(userInfo.updatedAt)}</span></div>
        <div><span>Downloaded</span><span>${limitDecimal(userInfo.totalDown/100000)} MB</span></div>
        <div><span>Uploaded</span><span>${limitDecimal(userInfo.totalUp/100000)} MB</span></div>
      </div>
      ${createSkillsSVGs(skills)}
    </div>

    <div id="RightSide">
      ${createHorizontalBars(
        userInfo.totalDown,
        userInfo.totalUp,
        userInfo.auditRatio
      )}
      ${createPieChart(passCount, failCount, nullCount)}
      ${createDonutChart(statusCounts)}
    </div>
  </div>

  <div id="bottom">
    <button id="toggleButton" class="btn">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
        <line x1="9" y1="9" x2="15" y2="15"></line>
        <line x1="15" y1="9" x2="9" y2="15"></line>
      </svg>
      Show Projects
    </button>
    ${userName === "amali" ? `
      <button id="toggleButton1" class="btn">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
          <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
        </svg>
        Ranking Table
      </button>
    ` : ''}
  </div>

  <div id="projectsTable" style="display: none;">
    <p>📊 Projects Overview</p>
    ${createProjectsTable(projects)}
  </div>

  <div id="usersByXP" style="display: none;">
    <p>🏆 Leaderboard</p>
    <div class="ranking-controls">
      <label for="userCountSelect">Show:</label>
      <select id="userCountSelect">
        <option value="10">Top 10</option>
        <option value="all">All Users</option>
      </select>
    </div>
    <div id="usersByXPTable">
      ${createUsersByXPTable(usersByXP, 10)}
    </div>
  </div>

  `;

  const logoutButton = document.getElementById("logoutButton");
  logoutButton.addEventListener("click", logout);

  document.getElementById('toggleButton').addEventListener('click', function() {
    const projectsTable = document.getElementById('projectsTable');
    const usersByXP = document.getElementById('usersByXP');
    const button = this;

    // Add loading state
    button.classList.add('loading');

    setTimeout(() => {
      if (projectsTable.style.display === 'none') {
        projectsTable.style.display = 'block';
        usersByXP.style.display = 'none';
        button.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="6,9 12,15 18,9"></polyline>
          </svg>
          Hide Projects
        `;
        if (document.getElementById('toggleButton1')) {
          const rankingButton = document.getElementById('toggleButton1');
          rankingButton.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
              <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
            </svg>
            Ranking Table
          `;
        }
      } else {
        projectsTable.style.display = 'none';
        button.innerHTML = `
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="9" y1="9" x2="15" y2="15"></line>
            <line x1="15" y1="9" x2="9" y2="15"></line>
          </svg>
          Show Projects
        `;
      }
      button.classList.remove('loading');
    }, 200);
  });

  if (userName === "amali") {
    document.getElementById('toggleButton1').addEventListener('click', function() {
      const usersByXP = document.getElementById('usersByXP');
      const projectsTable = document.getElementById('projectsTable');
      const button = this;

      // Add loading state
      button.classList.add('loading');

      setTimeout(() => {
        if (usersByXP.style.display === 'none') {
          usersByXP.style.display = 'block';
          projectsTable.style.display = 'none';
          button.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="6,9 12,15 18,9"></polyline>
            </svg>
            Hide Ranking Table
          `;
          const projectButton = document.getElementById('toggleButton');
          projectButton.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="9" y1="9" x2="15" y2="15"></line>
              <line x1="15" y1="9" x2="9" y2="15"></line>
            </svg>
            Show Projects
          `;
        } else {
          usersByXP.style.display = 'none';
          button.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
              <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
            </svg>
            Ranking Table
          `;
        }
        button.classList.remove('loading');
      }, 200);
    });

    document.getElementById('userCountSelect').addEventListener('change', (event) => {
      const selectedValue = event.target.value;
      const usersByXPTableDiv = document.getElementById('usersByXPTable');
      usersByXPTableDiv.innerHTML = createUsersByXPTable(usersByXP, selectedValue === 'all' ? 'all' : parseInt(selectedValue));
    });
  }
}

//**************************** helpers ****************************/

// Function to format timestamp to day/month/year
function formatDate(timestamp) {
  const date = new Date(timestamp);
  const day = date.getDate();
  const month = date.getMonth() + 1; // January is 0
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

//function to limit any number to one decimal place
function limitDecimal(num, dec = 1) {
  return num.toFixed(dec);
}

//**************************** SVGs ****************************/

// Function to create SVG horizontal bars
function createHorizontalBars(totalReceived, totalDone, auditRatio) {
  // Find the maximum value among totalReceived, totalDone, and auditRatio
  const maxValue = Math.max(totalReceived, totalDone);

  const maxWidth = 200; // Add some padding for better visualization
  const maxHeight = 25; // Height of each bar
  const barSpacing = 10; // Spacing between bars

  // Calculate bar widths based on values
  const totalReceivedWidth = (totalReceived / maxValue) * maxWidth;
  const totalDoneWidth = (totalDone / maxValue) * maxWidth;

  // Create SVG markup for bars
  const svgMarkup = `
  <div class="svg-container">
    <svg id="svg" width="${maxWidth}" height="${
    maxHeight * 3 + barSpacing * 3
  }";">
        <text x="120" y="${barSpacing}"  fill="#FFFFFF" dominant-baseline="middle" text-anchor="middle">Audit Ratio:${limitDecimal(auditRatio,2)}</text>
        <rect x="10" y="${
          barSpacing * 2
        }" width="${totalDoneWidth}" height="${maxHeight}" fill="#FFFFFF" />
        <text x="${totalDoneWidth / 2}" y="${
    barSpacing * 2 + 2 + maxHeight / 2
  }" fill="#000000" dominant-baseline="middle" text-anchor="middle">${
    limitDecimal(totalDone / 1000000,2)
  } MB</text>
        <text x="${totalDoneWidth + 23}" y="${
    barSpacing * 2 + maxHeight / 2
  }" fill="#FFFFFF" dominant-baseline="middle">Done</text>

        <rect x="10" y="${
          barSpacing * 3 + maxHeight
        }" width="${totalReceivedWidth}" height="${maxHeight}" fill="#F0BB00" />
        <text x="${totalReceivedWidth / 2}" y="${
    barSpacing * 3 + 2 + maxHeight + maxHeight / 2
  }" fill="#000000" dominant-baseline="middle" text-anchor="middle">${
    limitDecimal(totalReceived / 1000000,2)
  } MB</text>
        <text x="${totalReceivedWidth + 20}" y="${
    barSpacing * 3 + maxHeight + maxHeight / 2
  }" fill="#FFFFFF" dominant-baseline="middle">Received</text>
    </svg>
  </div>
`;

  return svgMarkup;
}

// Function to create SVG pie chart where passCount green, failCount red, nullCount gray
function createPieChart(passCount, failCount, nullCount) {
  const total = passCount + failCount + nullCount;

  // Calculate angles
  const passAngle = (passCount / total) * 360;
  const failAngle = (failCount / total) * 360;

  // Helper function to calculate the coordinates for arc
  function polarToCartesian(centerX, centerY, radius, angleInDegrees) {
      const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
      return {
          x: centerX + (radius * Math.cos(angleInRadians)),
          y: centerY + (radius * Math.sin(angleInRadians))
      };
  }

  // Helper function to create arc path
  function describeArc(x, y, radius, startAngle, endAngle) {
      const start = polarToCartesian(x, y, radius, endAngle);
      const end = polarToCartesian(x, y, radius, startAngle);
      const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

      return [
          "M", start.x, start.y,
          "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y,
          "L", x, y,
          "Z"
      ].join(" ");
  }

  // SVG dimensions and radius
  const width = 300;
  const height = 200;
  const radius = Math.min(width, height) / 3;
  const legendX = 200;
  const legendY = 50;
  const legendSpacing = 20;

  // Calculate paths
    const passPath = describeArc(width / 2 - 80, height / 2, radius, 0, passAngle);
    const failPath = describeArc(width / 2 - 80, height / 2, radius, passAngle, passAngle + failAngle);
    const nullPath = describeArc(width / 2 - 80, height / 2, radius, passAngle + failAngle, 360);

  // Create SVG markup
  const svgMarkup = `
      <svg id="svg-pie" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
          <path d="${passPath}" fill="#4CAF50"></path>
          <path d="${failPath}" fill="#D7263D"></path>
          <path d="${nullPath}" fill="#7D7D7D"></path>
          <!-- Legend -->
          <text x="${legendX-30}" y="${legendY}" font-family="Arial" font-size="18" fill="white">Audit Status</text>
          <rect x="${legendX - 30}" y="${legendY + 30}" width="20" height="20" fill="#4CAF50"></rect>
          <text x="${legendX}" y="${legendY + 45}" font-family="Arial" font-size="14" fill="white">Not Guilty:${passCount}</text>
          <rect x="${legendX-30}" y="${legendY + 30 + legendSpacing}" width="20" height="20" fill="#D7263D"></rect>
          <text x="${legendX}" y="${legendY + 45 + legendSpacing}" font-family="Arial" font-size="14" fill="white">Guilty:${failCount}</text>
          <rect x="${legendX-30}" y="${legendY + 30 + 2 * legendSpacing}" width="20" height="20" fill="#7D7D7D"></rect>
          <text x="${legendX}" y="${legendY + 45 + 2 * legendSpacing}" font-family="Arial" font-size="14" fill="white">Wanted:${nullCount}</text>
          <text x="${legendX}" y="${legendY + 45 + 3 * legendSpacing}" font-family="Arial" font-size="16" fill="white">Total:${total}</text>
      </svg>
  `;

  return svgMarkup;
}

// Function to create SVG donut chart where finished is green, working is yellow, and setup is blue
function createDonutChart(statusCounts) {
  const total = statusCounts.finished + statusCounts.working + statusCounts.setup;

  // Calculate angles
  const finishedAngle = (statusCounts.finished / total) * 360;
  const workingAngle = (statusCounts.working / total) * 360;

  // Helper function to calculate the coordinates for arc
  function polarToCartesian(centerX, centerY, radius, angleInDegrees) {
      const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
      return {
          x: centerX + (radius * Math.cos(angleInRadians)),
          y: centerY + (radius * Math.sin(angleInRadians))
      };
  }

  // Helper function to create arc path
  function describeArc(x, y, radius, startAngle, endAngle) {
      const start = polarToCartesian(x, y, radius, endAngle);
      const end = polarToCartesian(x, y, radius, startAngle);
      const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

      return [
          "M", start.x, start.y,
          "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y
      ].join(" ");
  }

  // SVG dimensions and radius
  const width = 300;
  const height = 200;
  const outerRadius = (Math.min(width, height) / 3) * 0.8; // 20% smaller
  const innerRadius = (outerRadius / 1.75) * 0.8; // 20% smaller
  const centerX = width / 2 - 60;
  const centerY = height / 2;
  const legendX = 200;
  const legendY = 20;
  const legendSpacing = 20;

  // Calculate paths
  const finishedPath = describeArc(centerX, centerY, outerRadius, 0, finishedAngle);
  const workingPath = describeArc(centerX, centerY, outerRadius, finishedAngle, finishedAngle + workingAngle);
  const setupPath = describeArc(centerX, centerY, outerRadius, finishedAngle + workingAngle, 360);

  // Create SVG markup
  const svgMarkup = `
      <svg id="svg-donut" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
          <g fill="none" stroke-width="${outerRadius - innerRadius}">
              <path d="${finishedPath}" stroke="#4CAF50"></path>
              <path d="${workingPath}" stroke="#F0BB00"></path>
              <path d="${setupPath}" stroke="#0078BF"></path>
          </g>
          <!-- Legend -->
          <text x="${legendX-30}" y="${legendY+10}" font-family="Arial" font-size="16" fill="white">Projects Status</text>
          <rect x="${legendX - 30}" y="${legendY + 40}" width="20" height="20" fill="#4CAF50"></rect>
          <text x="${legendX}" y="${legendY + 55}" font-family="Arial" font-size="14" fill="white">Finished: ${statusCounts.finished}</text>
          <rect x="${legendX-30}" y="${legendY + 40 + legendSpacing}" width="20" height="20" fill="#F0BB00"></rect>
          <text x="${legendX}" y="${legendY + 55 + legendSpacing}" font-family="Arial" font-size="14" fill="white">Working: ${statusCounts.working}</text>
          <rect x="${legendX-30}" y="${legendY + 40 + 2 * legendSpacing}" width="20" height="20" fill="#0078BF"></rect>
          <text x="${legendX}" y="${legendY + 55 + 2 * legendSpacing}" font-family="Arial" font-size="14" fill="white">Setup: ${statusCounts.setup}</text>
          <text x="${legendX}" y="${legendY + 55 + 3 * legendSpacing}" font-family="Arial" font-size="14" fill="white">Total: ${total}</text>
      </svg>
  `;
  return svgMarkup;
}

// Function to show case the user projects orgnsded by creation date shoiwng the project name,status, XP, and creation date
function createProjectsTable(projects) {
  // Create table header
  let tableHtml = `
    <table>
      <tr>
      <th>#</th>
        <th>Project Name</th>
        <th>Status</th>
        <th>XP</th>
        <th>Created At</th>
      </tr>
  `;

  // Add rows for each project
  projects.forEach((project) => {
    tableHtml += `
      <tr>
        <td>${projects.indexOf(project) + 1}</td>
        <td>${project.name}</td>
        <td>${project.status}</td>
        <td>${project.xp}</td>
        <td>${formatDate(project.createdAt)}</td>
      </tr>
    `;
  });

  // Close the table
  tableHtml += "</table>";

  return tableHtml;
}

// Function to show the users ranked by XP
function createUsersByXPTable(usersByXP, maxUsersToShow = 10) {
  // Create table header
  let tableHtml = `
    <table>
      <tr>
        <th>#</th>
        <th>User Name</th>
        <th>XP (MB)</th>
      </tr>
  `;

  // Determine the number of users to display
  const usersToShow = maxUsersToShow === 'all' ? usersByXP.length : Math.min(maxUsersToShow, usersByXP.length);

  // Add rows for each user
  for (let i = 0; i < usersToShow; i++) {
    const user = usersByXP[i];
    // Convert XP to MB format (divide by 1000000 to match personal XP display format)
    const formattedTotalXP = (user.totalXP / 1000000).toFixed(1);

    tableHtml += `
      <tr>
        <td>${i + 1}</td>
        <td>${user.user}</td>
        <td>${formattedTotalXP}</td>
      </tr>
    `;
  }

  // Close the table
  tableHtml += "</table>";

  return tableHtml;
}


// Function to create SVGs for skills
function createSkillsSVGs(skills) {
  if (!skills || skills.length === 0) {
    console.error("Skills array is empty or undefined");
    return '<div id="svg-skills"><h3>🛠️ Skills Overview</h3><p>No skills data available</p></div>';
  }

  const maxSkillAmount = Math.max(...skills.map(skill => skill.amount));
  const svgWidth = 280;
  const barHeight = 12;
  const barSpacing = 16;

  // Modern color palette for skills
  const skillColors = [
    '#4f46e5', '#06b6d4', '#10b981', '#f59e0b', '#ef4444',
    '#8b5cf6', '#ec4899', '#14b8a6', '#f97316', '#84cc16',
    '#6366f1', '#0ea5e9', '#22c55e', '#eab308', '#dc2626'
  ];

  function getSkillColor(index) {
    return skillColors[index % skillColors.length];
  }

  const svgHeight = skills.length * (barHeight + barSpacing) + 40;

  const svgMarkup = `
    <div id="svg-skills">
      <h3>🛠️ Skills Overview</h3>
      <svg width="${svgWidth}" height="${svgHeight}" style="overflow: visible;">
        <defs>
          <linearGradient id="skillGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#4f46e5;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
          </linearGradient>
        </defs>
        ${skills.map((skill, index) => {
          const barWidth = Math.max((skill.amount / maxSkillAmount) * (svgWidth - 100), 20);
          const yPosition = index * (barHeight + barSpacing) + 20;
          const color = getSkillColor(index);
          const percentage = Math.round((skill.amount / maxSkillAmount) * 100);

          return `
            <g class="skill-bar" data-skill="${skill.skill}">
              <!-- Background bar -->
              <rect x="0" y="${yPosition}" width="${svgWidth - 100}" height="${barHeight}"
                    fill="rgba(255,255,255,0.1)" rx="6" />
              <!-- Progress bar -->
              <rect x="0" y="${yPosition}" width="${barWidth}" height="${barHeight}"
                    fill="${color}" rx="6">
                <animate attributeName="width" from="0" to="${barWidth}" dur="1s" begin="0s" />
              </rect>
              <!-- Skill name -->
              <text x="0" y="${yPosition - 4}" fill="#cbd5e1" font-size="11" font-weight="500">
                ${skill.skill}
              </text>
              <!-- Percentage -->
              <text x="${svgWidth - 95}" y="${yPosition + 8}" fill="#f8fafc" font-size="10"
                    text-anchor="start" dominant-baseline="middle">
                ${skill.amount}%
              </text>
            </g>
          `;
        }).join('')}
      </svg>
    </div>
  `;

  return svgMarkup;
}




//**************************** login/logout ****************************/

// Function to handle logout
export async function logout() {
  const data = {
      "headers": {
          "x-jwt-token": sessionStorage.getItem('jwt') || '',
      },
      "method": 'GET',
  }

  await fetch(`https://learn.reboot01.com/api/auth/expire`, data);

  data.method = 'POST';
  await fetch(`https://learn.reboot01.com/api/auth/signout`, data);
  sessionStorage.removeItem("jwt");
  renderLoginPage();
}

// Function to check if the user is logged in
function checkLoginStatus() {
  const jwt = sessionStorage.getItem("jwt");
  if (jwt) {
    renderChartsPage(jwt);
  } else {
    renderLoginPage();
  }
}

// Initial render
checkLoginStatus();
