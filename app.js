import { GetJWT } from "./auth.js";
import { GetUserID } from "./myQuery.js";
import { GetUserInfo } from "./myQuery.js";
import { GetAuditStatus } from "./myQuery.js";
import {GetEventId} from "./myQuery.js";
import {GetUsersXP} from "./myQuery.js";
import { GetProjectsStatus } from "./myQuery.js";
import {GetCohortUsers} from "./myQuery.js";
import {UsersByXP} from "./myQuery.js";
import {getUserXpAndLevel} from "./myQuery.js";
import {getUserSkills} from "./myQuery.js";

//**************************** home page ****************************/

// Function to render the login page
function renderLoginPage() {
  const app = document.getElementById("app");
  app.innerHTML = `
    <div id="loginPage">
      <form id="loginForm">
      <h2>Login</h2>

        <label for="usernameOrEmail">Username or Email:</label>
        <input type="text" id="usernameOrEmail" name="usernameOrEmail" required placeholder="username or email">

        <br>
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required placeholder="password">
        <br>
        <button type="submit" id="loginButton">Login</button>
      </form>
    </div>
  `;

  const loginForm = document.getElementById("loginForm");
  loginForm.addEventListener("submit", async (event) => {
    event.preventDefault(); // Prevent form submission

    // Get the username/email and password from the form
    const usernameOrEmail = event.target.usernameOrEmail.value;
    const password = event.target.password.value;

    try {
      const temp_jwt = await GetJWT(usernameOrEmail, password);
      sessionStorage.setItem("jwt", temp_jwt);

      renderChartsPage(temp_jwt);
    } catch (error) {
      console.error("Error obtaining JWT:", error.message);
    }
  });
}

//*********************************************************************/
//**************************** Chart page ****************************/

// Function to render the charts page
async function renderChartsPage(jwt) {
  // Call UserInfo function to get user info
  const { userID, userName } = await GetUserID(jwt);
  // call GetUserInfo function to get user info
  const { userInfo } = await GetUserInfo(userName, jwt);
  const { passCount, failCount, nullCount}= await GetAuditStatus(jwt);
  const { eventId }= await GetEventId(jwt,userName);
  const { usersXP } = await GetUsersXP(jwt);
  const { statusCounts, projects } = await GetProjectsStatus(userName,eventId,usersXP,jwt);
  const { users } = await GetCohortUsers(jwt,eventId);
  const { xp, level } = await getUserXpAndLevel(userName, eventId, jwt);
  const skills = await getUserSkills(userID, jwt)
  // console.log("cohortUsers:", users);

  // Check if cohortUsers is not empty
  if (!users || users.length === 0) {
    throw new Error("Cohort users are undefined or empty.");
  }

  // Fetch users by XP and render charts page
  const { usersByXP } = await UsersByXP(jwt, eventId, users, usersXP);
  // console.log(usersByXP);


  const app = document.getElementById("app");
  app.innerHTML = `
  <div id="welcome">
    <p>Welcome, ${userName} (ID: ${userID})</p>
    <p>XP: ${xp/1000000} MB </p> <p>Level: ${level}</p>
    <p>Event ID: ${eventId}</p>
    <button id="logoutButton">Logout</button>
  </div>
  <div id="chartsPage">

    <div id="leftSide">
      <div id="userInfo">
        <p>User Info:</p>
        <div>First Name: ${userInfo.firstName}</div>
        <div>Last Name: ${userInfo.lastName}</div>
        <div>Email: ${userInfo.email}</div>
        <div>Campus: ${userInfo.campus}</div>
        <div>Joined at: ${formatDate(userInfo.createdAt)}</div>
        <div>Last update at: ${formatDate(userInfo.updatedAt)}</div>
        <div>totalDown: ${limitDecimal(userInfo.totalDown/100000)} MB</div>
        <div>totalUp: ${limitDecimal(userInfo.totalUp/100000)} MB</div>
      </div>
      ${createSkillsSVGs(skills)}

    </div>

    <div id="RightSide">
    ${createHorizontalBars(
      userInfo.totalDown,
      userInfo.totalUp,
      userInfo.auditRatio
    )}
      ${createPieChart(passCount, failCount, nullCount)}
      ${createDonutChart(statusCounts)}

      <!-- more Charts can be add here to be in the right  -->

    </div>
  </div>
    <div id="bottom">
      <button id="toggleButton">Show Projects</button>
      ${userName === "amali" ? '<button id="toggleButton1">Ranking Table</button>' : ''}
    </div>
    <div id="projectsTable" style="display: none;">
      <p>Projects:</p>
      ${createProjectsTable(projects)}
    </div>
    <!-- Render the button only if the user is "amali" -->
    <div id="usersByXP" style="display: none;">
      <p>Users by XP:</p>
      <label for="userCountSelect">Show:</label>
      <select id="userCountSelect">
        <option value="10">Top 10</option>
        <option value="all">All</option>
      </select>
      <div id="usersByXPTable">
        ${createUsersByXPTable(usersByXP, 10)}
      </div>
    </div>
    <!-- more Charts can be add here to be in the bottom  -->
    <p>One day more charts will be added here...</p>

  `;

  const logoutButton = document.getElementById("logoutButton");
  logoutButton.addEventListener("click", logout);

  document.getElementById('toggleButton').addEventListener('click', function() {
    const projectsTable = document.getElementById('projectsTable');
    const usersByXP = document.getElementById('usersByXP');
    if (projectsTable.style.display === 'none') {
      projectsTable.style.display = 'block';
      usersByXP.style.display = 'none';
      this.textContent = 'Hide Projects';
      if (document.getElementById('toggleButton1')) {
        document.getElementById('toggleButton1').textContent = 'Ranking Table';
      }
    } else {
      projectsTable.style.display = 'none';
      this.textContent = 'Show Projects';
    }
  });

  if (userName === "amali") {
    document.getElementById('toggleButton1').addEventListener('click', function() {
      const usersByXP = document.getElementById('usersByXP');
      const projectsTable = document.getElementById('projectsTable');
      if (usersByXP.style.display === 'none') {
        usersByXP.style.display = 'block';
        projectsTable.style.display = 'none';
        this.textContent = 'Hide Ranking Table';
        document.getElementById('toggleButton').textContent = 'Show Projects';
      } else {
        usersByXP.style.display = 'none';
        this.textContent = 'Ranking Table';
      }
    });

    document.getElementById('userCountSelect').addEventListener('change', (event) => {
      const selectedValue = event.target.value;
      const usersByXPTableDiv = document.getElementById('usersByXPTable');
      usersByXPTableDiv.innerHTML = createUsersByXPTable(usersByXP, selectedValue === 'all' ? 'all' : parseInt(selectedValue));
    });
  }
}

//**************************** helpers ****************************/

// Function to format timestamp to day/month/year
function formatDate(timestamp) {
  const date = new Date(timestamp);
  const day = date.getDate();
  const month = date.getMonth() + 1; // January is 0
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

//function to limit any number to one decimal place
function limitDecimal(num, dec = 1) {
  return num.toFixed(dec);
}

//**************************** SVGs ****************************/

// Function to create SVG horizontal bars
function createHorizontalBars(totalReceived, totalDone, auditRatio) {
  // Find the maximum value among totalReceived, totalDone, and auditRatio
  const maxValue = Math.max(totalReceived, totalDone);

  const maxWidth = 200; // Add some padding for better visualization
  const maxHeight = 25; // Height of each bar
  const barSpacing = 10; // Spacing between bars

  // Calculate bar widths based on values
  const totalReceivedWidth = (totalReceived / maxValue) * maxWidth;
  const totalDoneWidth = (totalDone / maxValue) * maxWidth;

  // Create SVG markup for bars
  const svgMarkup = `
  <div class="svg-container">
    <svg id="svg" width="${maxWidth}" height="${
    maxHeight * 3 + barSpacing * 3
  }";">
        <text x="120" y="${barSpacing}"  fill="#FFFFFF" dominant-baseline="middle" text-anchor="middle">Audit Ratio:${limitDecimal(auditRatio,2)}</text>
        <rect x="10" y="${
          barSpacing * 2
        }" width="${totalDoneWidth}" height="${maxHeight}" fill="#FFFFFF" />
        <text x="${totalDoneWidth / 2}" y="${
    barSpacing * 2 + 2 + maxHeight / 2
  }" fill="#000000" dominant-baseline="middle" text-anchor="middle">${
    limitDecimal(totalDone / 1000000,2)
  } MB</text>
        <text x="${totalDoneWidth + 23}" y="${
    barSpacing * 2 + maxHeight / 2
  }" fill="#FFFFFF" dominant-baseline="middle">Done</text>

        <rect x="10" y="${
          barSpacing * 3 + maxHeight
        }" width="${totalReceivedWidth}" height="${maxHeight}" fill="#F0BB00" />
        <text x="${totalReceivedWidth / 2}" y="${
    barSpacing * 3 + 2 + maxHeight + maxHeight / 2
  }" fill="#000000" dominant-baseline="middle" text-anchor="middle">${
    limitDecimal(totalReceived / 1000000,2)
  } MB</text>
        <text x="${totalReceivedWidth + 20}" y="${
    barSpacing * 3 + maxHeight + maxHeight / 2
  }" fill="#FFFFFF" dominant-baseline="middle">Received</text>
    </svg>
  </div>
`;

  return svgMarkup;
}

// Function to create SVG pie chart where passCount green, failCount red, nullCount gray
function createPieChart(passCount, failCount, nullCount) {
  const total = passCount + failCount + nullCount;

  // Calculate angles
  const passAngle = (passCount / total) * 360;
  const failAngle = (failCount / total) * 360;

  // Helper function to calculate the coordinates for arc
  function polarToCartesian(centerX, centerY, radius, angleInDegrees) {
      const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
      return {
          x: centerX + (radius * Math.cos(angleInRadians)),
          y: centerY + (radius * Math.sin(angleInRadians))
      };
  }

  // Helper function to create arc path
  function describeArc(x, y, radius, startAngle, endAngle) {
      const start = polarToCartesian(x, y, radius, endAngle);
      const end = polarToCartesian(x, y, radius, startAngle);
      const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

      return [
          "M", start.x, start.y,
          "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y,
          "L", x, y,
          "Z"
      ].join(" ");
  }

  // SVG dimensions and radius
  const width = 300;
  const height = 200;
  const radius = Math.min(width, height) / 3;
  const legendX = 200;
  const legendY = 50;
  const legendSpacing = 20;

  // Calculate paths
    const passPath = describeArc(width / 2 - 80, height / 2, radius, 0, passAngle);
    const failPath = describeArc(width / 2 - 80, height / 2, radius, passAngle, passAngle + failAngle);
    const nullPath = describeArc(width / 2 - 80, height / 2, radius, passAngle + failAngle, 360);

  // Create SVG markup
  const svgMarkup = `
      <svg id="svg-pie" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
          <path d="${passPath}" fill="#4CAF50"></path>
          <path d="${failPath}" fill="#D7263D"></path>
          <path d="${nullPath}" fill="#7D7D7D"></path>
          <!-- Legend -->
          <text x="${legendX-30}" y="${legendY}" font-family="Arial" font-size="18" fill="white">Audit Status</text>
          <rect x="${legendX - 30}" y="${legendY + 30}" width="20" height="20" fill="#4CAF50"></rect>
          <text x="${legendX}" y="${legendY + 45}" font-family="Arial" font-size="14" fill="white">Not Guilty:${passCount}</text>
          <rect x="${legendX-30}" y="${legendY + 30 + legendSpacing}" width="20" height="20" fill="#D7263D"></rect>
          <text x="${legendX}" y="${legendY + 45 + legendSpacing}" font-family="Arial" font-size="14" fill="white">Guilty:${failCount}</text>
          <rect x="${legendX-30}" y="${legendY + 30 + 2 * legendSpacing}" width="20" height="20" fill="#7D7D7D"></rect>
          <text x="${legendX}" y="${legendY + 45 + 2 * legendSpacing}" font-family="Arial" font-size="14" fill="white">Wanted:${nullCount}</text>
          <text x="${legendX}" y="${legendY + 45 + 3 * legendSpacing}" font-family="Arial" font-size="16" fill="white">Total:${total}</text>
      </svg>
  `;

  return svgMarkup;
}

// Function to create SVG donut chart where finished is green, working is yellow, and setup is blue
function createDonutChart(statusCounts) {
  const total = statusCounts.finished + statusCounts.working + statusCounts.setup;

  // Calculate angles
  const finishedAngle = (statusCounts.finished / total) * 360;
  const workingAngle = (statusCounts.working / total) * 360;

  // Helper function to calculate the coordinates for arc
  function polarToCartesian(centerX, centerY, radius, angleInDegrees) {
      const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
      return {
          x: centerX + (radius * Math.cos(angleInRadians)),
          y: centerY + (radius * Math.sin(angleInRadians))
      };
  }

  // Helper function to create arc path
  function describeArc(x, y, radius, startAngle, endAngle) {
      const start = polarToCartesian(x, y, radius, endAngle);
      const end = polarToCartesian(x, y, radius, startAngle);
      const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

      return [
          "M", start.x, start.y,
          "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y
      ].join(" ");
  }

  // SVG dimensions and radius
  const width = 300;
  const height = 200;
  const outerRadius = (Math.min(width, height) / 3) * 0.8; // 20% smaller
  const innerRadius = (outerRadius / 1.75) * 0.8; // 20% smaller
  const centerX = width / 2 - 60;
  const centerY = height / 2;
  const legendX = 200;
  const legendY = 20;
  const legendSpacing = 20;

  // Calculate paths
  const finishedPath = describeArc(centerX, centerY, outerRadius, 0, finishedAngle);
  const workingPath = describeArc(centerX, centerY, outerRadius, finishedAngle, finishedAngle + workingAngle);
  const setupPath = describeArc(centerX, centerY, outerRadius, finishedAngle + workingAngle, 360);

  // Create SVG markup
  const svgMarkup = `
      <svg id="svg-donut" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
          <g fill="none" stroke-width="${outerRadius - innerRadius}">
              <path d="${finishedPath}" stroke="#4CAF50"></path>
              <path d="${workingPath}" stroke="#F0BB00"></path>
              <path d="${setupPath}" stroke="#0078BF"></path>
          </g>
          <!-- Legend -->
          <text x="${legendX-30}" y="${legendY+10}" font-family="Arial" font-size="16" fill="white">Projects Status</text>
          <rect x="${legendX - 30}" y="${legendY + 40}" width="20" height="20" fill="#4CAF50"></rect>
          <text x="${legendX}" y="${legendY + 55}" font-family="Arial" font-size="14" fill="white">Finished: ${statusCounts.finished}</text>
          <rect x="${legendX-30}" y="${legendY + 40 + legendSpacing}" width="20" height="20" fill="#F0BB00"></rect>
          <text x="${legendX}" y="${legendY + 55 + legendSpacing}" font-family="Arial" font-size="14" fill="white">Working: ${statusCounts.working}</text>
          <rect x="${legendX-30}" y="${legendY + 40 + 2 * legendSpacing}" width="20" height="20" fill="#0078BF"></rect>
          <text x="${legendX}" y="${legendY + 55 + 2 * legendSpacing}" font-family="Arial" font-size="14" fill="white">Setup: ${statusCounts.setup}</text>
          <text x="${legendX}" y="${legendY + 55 + 3 * legendSpacing}" font-family="Arial" font-size="14" fill="white">Total: ${total}</text>
      </svg>
  `;
  return svgMarkup;
}

// Function to show case the user projects orgnsded by creation date shoiwng the project name,status, XP, and creation date
function createProjectsTable(projects) {
  // Create table header
  let tableHtml = `
    <table>
      <tr>
      <th>#</th>
        <th>Project Name</th>
        <th>Status</th>
        <th>XP</th>
        <th>Created At</th>
      </tr>
  `;

  // Add rows for each project
  projects.forEach((project) => {
    tableHtml += `
      <tr>
        <td>${projects.indexOf(project) + 1}</td>
        <td>${project.name}</td>
        <td>${project.status}</td>
        <td>${project.xp}</td>
        <td>${formatDate(project.createdAt)}</td>
      </tr>
    `;
  });

  // Close the table
  tableHtml += "</table>";

  return tableHtml;
}

// Function to show the users ranked by XP
function createUsersByXPTable(usersByXP, maxUsersToShow = 10) {
  // Create table header
  let tableHtml = `
    <table>
      <tr>
        <th>#</th>
        <th>User Name</th>
        <th>XP</th>
      </tr>
  `;

  // Determine the number of users to display
  const usersToShow = maxUsersToShow === 'all' ? usersByXP.length : Math.min(maxUsersToShow, usersByXP.length);

  // Add rows for each user
  for (let i = 0; i < usersToShow; i++) {
    const user = usersByXP[i];
    // Limit totalXP to one decimal place
    const formattedTotalXP = user.totalXP.toFixed(1);

    tableHtml += `
      <tr>
        <td>${i + 1}</td>
        <td>${user.user}</td>
        <td>${formattedTotalXP}</td>
      </tr>
    `;
  }

  // Close the table
  tableHtml += "</table>";

  return tableHtml;
}


// Function to create SVGs for skills
function createSkillsSVGs(skills) {
  if (!skills || skills.length === 0) {
    console.error("Skills array is empty or undefined");
    return '';
  }

  const maxSkillAmount = Math.max(...skills.map(skill => skill.amount));
  const svgWidth = 300; // Width of the SVG
  const barHeight = 8; // Height of each bar
  const barSpacing = 10; // Space between bars

  // Function to generate a random color for each bar
  function getRandomColor() {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
      color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
  }

  const svgHeight = skills.length * (barHeight + barSpacing);

  const svgMarkup = `
    <svg id="svg-skills" width="${svgWidth}" height="${svgHeight+20}">
    <text x="40" y="10"  fill="#FFFFFF" dominant-baseline="middle" text-anchor="middle">User Skills</text>
      ${skills.map((skill, index) => {
        const barWidth = (skill.amount/2 / maxSkillAmount) * svgWidth;
        const yPosition = index * (barHeight + barSpacing)+25;
        const color = getRandomColor();
        return `
          <rect x="0" y="${yPosition}" width="${barWidth}" height="${barHeight}" fill="${color}" />
          <text x="${barWidth + 5}" y="${yPosition + barHeight}" fill="#FFFFFF" font-size="12" alignment-baseline="middle">${skill.skill}: ${skill.amount}%</text>
        `;
      }).join('')}
    </svg>
  `;

  return svgMarkup;
}




//**************************** login/logout ****************************/

// Function to handle logout
export async function logout() {
  const data = {
      "headers": {
          "x-jwt-token": sessionStorage.getItem('jwt') || '',
      },
      "method": 'GET',
  }

  await fetch(`https://learn.reboot01.com/api/auth/expire`, data);

  data.method = 'POST';
  await fetch(`https://learn.reboot01.com/api/auth/signout`, data);
  sessionStorage.removeItem("jwt");
  renderLoginPage();
}

// Function to check if the user is logged in
function checkLoginStatus() {
  const jwt = sessionStorage.getItem("jwt");
  if (jwt) {
    renderChartsPage(jwt);
  } else {
    renderLoginPage();
  }
}

// Initial render
checkLoginStatus();
