import { GetJWT } from "./auth.js";
import { GetUserID } from "./myQuery.js";
import { GetUserInfo } from "./myQuery.js";
import { GetAuditStatus } from "./myQuery.js";
import {GetEventId} from "./myQuery.js";
import {GetUsersXP} from "./myQuery.js";
import {GetProjectXPMapping} from "./myQuery.js";
import { GetProjectsStatus } from "./myQuery.js";
import {GetCohortUsers} from "./myQuery.js";
import {UsersByXP} from "./myQuery.js";
import {getUserXpAndLevel} from "./myQuery.js";
import {getUserSkills} from "./myQuery.js";

//**************************** home page ****************************/

// Function to render the login page
function renderLoginPage() {
  const app = document.getElementById("app");
  app.innerHTML = `
    <div id="loginPage">
      <form id="loginForm">
      <h2>Login</h2>

        <label for="usernameOrEmail">Username or Email:</label>
        <input type="text" id="usernameOrEmail" name="usernameOrEmail" required placeholder="username or email">

        <br>
        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required placeholder="password">
        <br>
        <button type="submit" id="loginButton">Login</button>
      </form>
    </div>
  `;

  const loginForm = document.getElementById("loginForm");
  loginForm.addEventListener("submit", async (event) => {
    event.preventDefault(); // Prevent form submission

    // Get the username/email and password from the form
    const usernameOrEmail = event.target.usernameOrEmail.value;
    const password = event.target.password.value;

    try {
      const temp_jwt = await GetJWT(usernameOrEmail, password);
      sessionStorage.setItem("jwt", temp_jwt);

      renderChartsPage(temp_jwt);
    } catch (error) {
      console.error("Error obtaining JWT:", error.message);
    }
  });
}

//*********************************************************************/
//**************************** Chart page ****************************/

// Function to render the charts page
async function renderChartsPage(jwt) {
  // Call UserInfo function to get user info
  const { userID, userName } = await GetUserID(jwt);
  // call GetUserInfo function to get user info
  const { userInfo } = await GetUserInfo(userName, jwt);
  const { passCount, failCount, nullCount}= await GetAuditStatus(jwt);
  const { eventId }= await GetEventId(jwt,userName);
  const { usersXP } = await GetUsersXP(jwt);
  const { projectXPMapping } = await GetProjectXPMapping(eventId, jwt);
  const { statusCounts, projects } = await GetProjectsStatus(userName,eventId,usersXP,jwt);
  const { users } = await GetCohortUsers(jwt,eventId);
  const { xp, level } = await getUserXpAndLevel(userName, eventId, jwt);
  const skills = await getUserSkills(userID, jwt)
  // console.log("cohortUsers:", users);

  // Check if cohortUsers is not empty
  if (!users || users.length === 0) {
    throw new Error("Cohort users are undefined or empty.");
  }

  // Fetch users by XP using comprehensive project XP mapping for accurate ranking
  const { usersByXP } = await UsersByXP(jwt, eventId, users, projectXPMapping);
  // console.log(usersByXP);


  const app = document.getElementById("app");
  app.innerHTML = `
    <!-- Mobile Sidebar Toggle -->
    <button class="sidebar-toggle" id="sidebarToggle">
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="3" y1="6" x2="21" y2="6"></line>
        <line x1="3" y1="12" x2="21" y2="12"></line>
        <line x1="3" y1="18" x2="21" y2="18"></line>
      </svg>
    </button>

    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
      <div class="sidebar-header">
        <h2>GraphQL Dashboard</h2>
      </div>
      <nav class="sidebar-nav">
        <a href="#" class="nav-item active" data-page="dashboard">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="7" height="7"></rect>
            <rect x="14" y="3" width="7" height="7"></rect>
            <rect x="14" y="14" width="7" height="7"></rect>
            <rect x="3" y="14" width="7" height="7"></rect>
          </svg>
          Dashboard
        </a>
        <a href="#" class="nav-item" data-page="projects">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14,2 14,8 20,8"></polyline>
            <line x1="16" y1="13" x2="8" y2="13"></line>
            <line x1="16" y1="17" x2="8" y2="17"></line>
            <polyline points="10,9 9,9 8,9"></polyline>
          </svg>
          Project Lists
        </a>
        <a href="#" class="nav-item" data-page="skills">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
          </svg>
          Skills
        </a>
        ${userName === "amali" ? `
          <a href="#" class="nav-item" data-page="leaderboard">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
              <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
            </svg>
            Leaderboard
          </a>
        ` : ''}
      </nav>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
      <!-- Welcome Header -->
      <div id="welcome">
        <div>
          <p>Welcome, ${userName}</p>
          <div class="welcome-stats">
            <span class="stat-item">ID: ${userID}</span>
            <span class="stat-item">XP: ${(xp/1000000).toFixed(2)} MB</span>
            <span class="stat-item">Level: ${level}</span>
            <span class="stat-item">Event: ${eventId}</span>
          </div>
        </div>
        <button id="logoutButton" class="btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
            <polyline points="16,17 21,12 16,7"></polyline>
            <line x1="21" y1="12" x2="9" y2="12"></line>
          </svg>
          Logout
        </button>
      </div>

      <!-- Dashboard Page -->
      <div id="dashboard-page" class="page active">
        <div class="dashboard-rows">
          <!-- Row 1: User Information and Audit Statistics -->
          <div class="dashboard-row row-1">
            <div id="userInfo">
              <p>👤 User Information</p>
              <div><span>First Name</span><span>${userInfo.firstName}</span></div>
              <div><span>Last Name</span><span>${userInfo.lastName}</span></div>
              <div><span>Email</span><span title="${userInfo.email}" class="email-field">${userInfo.email}</span></div>
              <div><span>Campus</span><span>${userInfo.campus}</span></div>
              <div><span>Joined</span><span>${formatDate(userInfo.createdAt)}</span></div>
              <div><span>Last Update</span><span>${formatDate(userInfo.updatedAt)}</span></div>
              <div><span>Downloaded</span><span>${limitDecimal(userInfo.totalDown/100000)} MB</span></div>
              <div><span>Uploaded</span><span>${limitDecimal(userInfo.totalUp/100000)} MB</span></div>
            </div>

            <div class="audit-statistics-container">
              ${createHorizontalBars(
                userInfo.totalDown,
                userInfo.totalUp,
                userInfo.auditRatio
              )}
            </div>
          </div>

          <!-- Row 2: Status Overview -->
          <div class="dashboard-row row-2">
            <div id="status-overview">
              <h3 class="chart-title">📊 Status Overview</h3>
              <div class="status-charts-grid">
                <div class="status-chart-wrapper">
                  ${createPieChart(passCount, failCount, nullCount)}
                </div>
                <div class="status-chart-wrapper">
                  ${createDonutChart(statusCounts)}
                </div>
              </div>
            </div>
          </div>

          <!-- Row 3: Top Skills -->
          <div class="dashboard-row row-3">
            <div id="topSkills">
              <p>🛠️ Top Skills</p>
              ${createSkillsRowsMarkup(skills)}
            </div>
          </div>
        </div>
      </div>

      <!-- Projects Page -->
      <div id="projects-page" class="page">
        <h2 class="page-title">📊 Projects Overview</h2>
        ${createProjectsTable(projects)}
      </div>

      <!-- Skills Page -->
      <div id="skills-page" class="page">
        <h2 class="page-title">🛠️ All Skills</h2>
        ${createAllSkillsPage(skills)}
      </div>

      <!-- Leaderboard Page -->
      ${userName === "amali" ? `
        <div id="leaderboard-page" class="page">
          <h2 class="page-title">🏆 Leaderboard</h2>
          <div class="leaderboard-tabs">
            <button class="tab-button active" data-count="10">Top 10</button>
            <button class="tab-button" data-count="25">Top 25</button>
            <button class="tab-button" data-count="all">All Users</button>
          </div>
          <div id="leaderboardTable">
            ${createUsersByXPTable(usersByXP, 10)}
          </div>
        </div>
      ` : ''}
    </div>
  `;

  // Initialize navigation
  initializeNavigation();

  // Logout functionality
  const logoutButton = document.getElementById("logoutButton");
  logoutButton.addEventListener("click", logout);

  // Leaderboard tabs functionality
  if (userName === "amali") {
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
      button.addEventListener('click', function() {
        // Remove active class from all tabs
        tabButtons.forEach(tab => tab.classList.remove('active'));
        // Add active class to clicked tab
        this.classList.add('active');

        // Update leaderboard table
        const count = this.dataset.count;
        const leaderboardTable = document.getElementById('leaderboardTable');
        leaderboardTable.innerHTML = createUsersByXPTable(usersByXP, count === 'all' ? 'all' : parseInt(count));
      });
    });
  }

// Navigation system
function initializeNavigation() {
  // Sidebar toggle for mobile
  const sidebarToggle = document.getElementById('sidebarToggle');
  const sidebar = document.getElementById('sidebar');

  sidebarToggle.addEventListener('click', () => {
    sidebar.classList.toggle('open');
  });

  // Navigation items
  const navItems = document.querySelectorAll('.nav-item');
  const pages = document.querySelectorAll('.page');

  // Function to navigate to a page
  function navigateToPage(targetPage) {
    // Remove active class from all nav items
    navItems.forEach(nav => nav.classList.remove('active'));
    // Add active class to target nav item
    const targetNavItem = document.querySelector(`[data-page="${targetPage}"]`);
    if (targetNavItem) {
      targetNavItem.classList.add('active');
    }

    // Hide all pages
    pages.forEach(page => page.classList.remove('active'));

    // Show selected page
    const targetElement = document.getElementById(`${targetPage}-page`);
    if (targetElement) {
      targetElement.classList.add('active');
    }

    // Close sidebar on mobile after navigation
    if (window.innerWidth <= 1024) {
      sidebar.classList.remove('open');
    }
  }

  navItems.forEach(item => {
    item.addEventListener('click', (e) => {
      e.preventDefault();
      const targetPage = item.dataset.page;
      navigateToPage(targetPage);
    });
  });

  // Skills count link navigation
  const skillsCountLink = document.querySelector('.skills-count-link');
  if (skillsCountLink) {
    skillsCountLink.addEventListener('click', (e) => {
      e.preventDefault();
      navigateToPage('skills');
    });
  }
}
}

//**************************** helpers ****************************/

// Function to format timestamp to day/month/year
function formatDate(timestamp) {
  const date = new Date(timestamp);
  const day = date.getDate();
  const month = date.getMonth() + 1; // January is 0
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

//function to limit any number to one decimal place
function limitDecimal(num, dec = 1) {
  return num.toFixed(dec);
}

//**************************** SVGs ****************************/

// Function to create SVG horizontal bars
function createHorizontalBars(totalReceived, totalDone, auditRatio) {
  // Find the maximum value among totalReceived, totalDone, and auditRatio
  const maxValue = Math.max(totalReceived, totalDone);

  const maxWidth = 280; // Increased width for better text display
  const maxHeight = 30; // Increased height for better visibility
  const barSpacing = 15; // Increased spacing

  // Calculate bar widths based on values
  const totalReceivedWidth = Math.max((totalReceived / maxValue) * (maxWidth - 80), 20);
  const totalDoneWidth = Math.max((totalDone / maxValue) * (maxWidth - 80), 20);

  // Create improved audit statistics markup
  const svgMarkup = `
  <div id="audit-statistics">
    <h3 class="chart-title">📊 Audit Statistics</h3>

    <div class="audit-ratio-display">
      <span class="audit-label">Audit Ratio:</span>
      <span class="audit-value">${limitDecimal(auditRatio, 2)}</span>
    </div>

    <div class="audit-bars-container">
      <!-- Done Bar Section -->
      <div class="audit-bar-section">
        <div class="audit-bar-label">
          <span class="bar-title">Done</span>
          <span class="bar-value">${limitDecimal(totalDone / 1000000, 2)} MB</span>
        </div>
        <div class="audit-progress-container">
          <div class="audit-progress-bg"></div>
          <div class="audit-progress-bar done-bar" style="width: ${(totalDoneWidth / (maxWidth - 80)) * 100}%">
            <div class="audit-progress-glow"></div>
          </div>
        </div>
      </div>

      <!-- Received Bar Section -->
      <div class="audit-bar-section">
        <div class="audit-bar-label">
          <span class="bar-title">Received</span>
          <span class="bar-value">${limitDecimal(totalReceived / 1000000, 2)} MB</span>
        </div>
        <div class="audit-progress-container">
          <div class="audit-progress-bg"></div>
          <div class="audit-progress-bar received-bar" style="width: ${(totalReceivedWidth / (maxWidth - 80)) * 100}%">
            <div class="audit-progress-glow"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
`;

  return svgMarkup;
}

// Function to create SVG pie chart where passCount green, failCount red, nullCount gray
function createPieChart(passCount, failCount, nullCount) {
  const total = passCount + failCount + nullCount;

  // Calculate angles
  const passAngle = (passCount / total) * 360;
  const failAngle = (failCount / total) * 360;

  // Helper function to calculate the coordinates for arc
  function polarToCartesian(centerX, centerY, radius, angleInDegrees) {
      const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
      return {
          x: centerX + (radius * Math.cos(angleInRadians)),
          y: centerY + (radius * Math.sin(angleInRadians))
      };
  }

  // Helper function to create arc path
  function describeArc(x, y, radius, startAngle, endAngle) {
      const start = polarToCartesian(x, y, radius, endAngle);
      const end = polarToCartesian(x, y, radius, startAngle);
      const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

      return [
          "M", start.x, start.y,
          "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y,
          "L", x, y,
          "Z"
      ].join(" ");
  }

  // SVG dimensions and radius
  const width = 250;
  const height = 250;
  const radius = Math.min(width, height) / 3.5;
  const centerX = width / 2;
  const centerY = height / 2;
  const legendX = 200;
  const legendY = 50;
  const legendSpacing = 20;

  // Calculate paths
    const passPath = describeArc(centerX, centerY, radius, 0, passAngle);
    const failPath = describeArc(centerX, centerY, radius, passAngle, passAngle + failAngle);
    const nullPath = describeArc(centerX, centerY, radius, passAngle + failAngle, 360);

  // Create modern pie chart markup
  const svgMarkup = `
    <div id="audit-status-chart">
      <h3 class="chart-title">⚖️ Audit Status</h3>
      <div class="pie-chart-container">
        <svg id="svg-pie" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
          <defs>
            <filter id="pieGlow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          <path d="${passPath}" fill="#10b981" filter="url(#pieGlow)" class="pie-slice">
            <animate attributeName="opacity" from="0" to="1" dur="0.8s" begin="0s" />
          </path>
          <path d="${failPath}" fill="#ef4444" filter="url(#pieGlow)" class="pie-slice">
            <animate attributeName="opacity" from="0" to="1" dur="0.8s" begin="0.2s" />
          </path>
          <path d="${nullPath}" fill="#6b7280" filter="url(#pieGlow)" class="pie-slice">
            <animate attributeName="opacity" from="0" to="1" dur="0.8s" begin="0.4s" />
          </path>
        </svg>

        <div class="pie-legend">
          <div class="legend-item">
            <div class="legend-color" style="background: #10b981;"></div>
            <div class="legend-text">
              <span class="legend-label">Not Guilty</span>
              <span class="legend-value">${passCount}</span>
            </div>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #ef4444;"></div>
            <div class="legend-text">
              <span class="legend-label">Guilty</span>
              <span class="legend-value">${failCount}</span>
            </div>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #6b7280;"></div>
            <div class="legend-text">
              <span class="legend-label">Wanted</span>
              <span class="legend-value">${nullCount}</span>
            </div>
          </div>
          <div class="legend-total">
            <span class="total-label">Total:</span>
            <span class="total-value">${total}</span>
          </div>
        </div>
      </div>
    </div>
  `;

  return svgMarkup;
}

// Function to create SVG donut chart where finished is green, working is yellow, and setup is blue
function createDonutChart(statusCounts) {
  const total = statusCounts.finished + statusCounts.working + statusCounts.setup;

  // Calculate angles
  const finishedAngle = (statusCounts.finished / total) * 360;
  const workingAngle = (statusCounts.working / total) * 360;

  // Helper function to calculate the coordinates for arc
  function polarToCartesian(centerX, centerY, radius, angleInDegrees) {
      const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
      return {
          x: centerX + (radius * Math.cos(angleInRadians)),
          y: centerY + (radius * Math.sin(angleInRadians))
      };
  }

  // Helper function to create arc path
  function describeArc(x, y, radius, startAngle, endAngle) {
      const start = polarToCartesian(x, y, radius, endAngle);
      const end = polarToCartesian(x, y, radius, startAngle);
      const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

      return [
          "M", start.x, start.y,
          "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y
      ].join(" ");
  }

  // SVG dimensions and radius
  const width = 250;
  const height = 250;
  const outerRadius = Math.min(width, height) / 3.5;
  const innerRadius = outerRadius / 2.2;
  const centerX = width / 2;
  const centerY = height / 2;
  const legendX = 200;
  const legendY = 20;
  const legendSpacing = 20;

  // Calculate paths
  const finishedPath = describeArc(centerX, centerY, outerRadius, 0, finishedAngle);
  const workingPath = describeArc(centerX, centerY, outerRadius, finishedAngle, finishedAngle + workingAngle);
  const setupPath = describeArc(centerX, centerY, outerRadius, finishedAngle + workingAngle, 360);

  // Create modern donut chart markup
  const svgMarkup = `
    <div id="projects-status-chart">
      <h3 class="chart-title">📊 Projects Status</h3>
      <div class="donut-chart-container">
        <svg id="svg-donut" width="${width}" height="${height}" viewBox="0 0 ${width} ${height}">
          <defs>
            <filter id="donutGlow">
              <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          <g fill="none" stroke-width="${outerRadius - innerRadius}" filter="url(#donutGlow)">
              <path d="${finishedPath}" stroke="#10b981" class="donut-segment">
                <animate attributeName="stroke-dasharray" from="0 1000" to="1000 0" dur="1s" begin="0s" />
              </path>
              <path d="${workingPath}" stroke="#f59e0b" class="donut-segment">
                <animate attributeName="stroke-dasharray" from="0 1000" to="1000 0" dur="1s" begin="0.3s" />
              </path>
              <path d="${setupPath}" stroke="#3b82f6" class="donut-segment">
                <animate attributeName="stroke-dasharray" from="0 1000" to="1000 0" dur="1s" begin="0.6s" />
              </path>
          </g>
          <!-- Center text -->
          <text x="${centerX}" y="${centerY - 5}" text-anchor="middle" font-size="16" fill="#f8fafc" font-weight="600">Total</text>
          <text x="${centerX}" y="${centerY + 15}" text-anchor="middle" font-size="24" fill="#4f46e5" font-weight="700">${total}</text>
        </svg>

        <div class="donut-legend">
          <div class="legend-item">
            <div class="legend-color" style="background: #10b981;"></div>
            <div class="legend-text">
              <span class="legend-label">Finished</span>
              <span class="legend-value">${statusCounts.finished}</span>
            </div>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #f59e0b;"></div>
            <div class="legend-text">
              <span class="legend-label">Working</span>
              <span class="legend-value">${statusCounts.working}</span>
            </div>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: #3b82f6;"></div>
            <div class="legend-text">
              <span class="legend-label">Setup</span>
              <span class="legend-value">${statusCounts.setup}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
  return svgMarkup;
}

// Function to showcase user projects organized by creation date showing project name, status, XP, and creation date
function createProjectsTable(projects) {
  // Function to get status badge
  function getStatusBadge(status) {
    const statusMap = {
      'finished': { color: '#10b981', icon: '✅', text: 'Completed' },
      'in-progress': { color: '#f59e0b', icon: '🔄', text: 'In Progress' },
      'pending': { color: '#6b7280', icon: '⏳', text: 'Pending' },
      'failed': { color: '#ef4444', icon: '❌', text: 'Failed' }
    };

    const statusInfo = statusMap[status] || { color: '#6b7280', icon: '❓', text: status };

    return `
      <span class="status-badge" style="background: ${statusInfo.color}20; color: ${statusInfo.color}; border: 1px solid ${statusInfo.color}40;">
        ${statusInfo.icon} ${statusInfo.text}
      </span>
    `;
  }

  // Create table header
  let tableHtml = `
    <div class="table-container">
      <table class="projects-table">
        <thead>
          <tr>
            <th>#</th>
            <th>Project Name</th>
            <th>Status</th>
            <th>XP Earned</th>
            <th>Created Date</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for each project
  projects.forEach((project, index) => {
    tableHtml += `
      <tr class="project-row">
        <td class="project-number">${index + 1}</td>
        <td class="project-name">
          <div class="project-name-container">
            <span class="project-title">${project.name}</span>
          </div>
        </td>
        <td class="project-status">${getStatusBadge(project.status)}</td>
        <td class="project-xp">
          <span class="xp-value">${project.xp}</span>
          <span class="xp-unit">XP</span>
        </td>
        <td class="project-date">${formatDate(project.createdAt)}</td>
      </tr>
    `;
  });

  // Close the table
  tableHtml += `
        </tbody>
      </table>
    </div>
  `;

  return tableHtml;
}

// Function to show the users ranked by XP
function createUsersByXPTable(usersByXP, maxUsersToShow = 10) {
  // Function to get rank badge
  function getRankBadge(rank) {
    if (rank === 1) return '🥇';
    if (rank === 2) return '🥈';
    if (rank === 3) return '🥉';
    return `#${rank}`;
  }

  // Function to get rank class
  function getRankClass(rank) {
    if (rank <= 3) return 'top-rank';
    if (rank <= 10) return 'high-rank';
    return 'normal-rank';
  }

  // Create table header
  let tableHtml = `
    <div class="table-container">
      <table class="leaderboard-table">
        <thead>
          <tr>
            <th>Rank</th>
            <th>User</th>
            <th>XP (MB)</th>
            <th>Progress</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Determine the number of users to display
  const usersToShow = maxUsersToShow === 'all' ? usersByXP.length : Math.min(maxUsersToShow, usersByXP.length);
  const maxXP = usersByXP.length > 0 ? usersByXP[0].totalXP : 1;

  // Add rows for each user
  for (let i = 0; i < usersToShow; i++) {
    const user = usersByXP[i];
    const rank = i + 1;
    const formattedTotalXP = (user.totalXP / 1000000).toFixed(1);
    const progressPercentage = Math.round((user.totalXP / maxXP) * 100);

    tableHtml += `
      <tr class="leaderboard-row ${getRankClass(rank)}">
        <td class="rank-cell">
          <span class="rank-badge">${getRankBadge(rank)}</span>
        </td>
        <td class="user-cell">
          <div class="user-info">
            <div class="user-avatar">${user.user.charAt(0).toUpperCase()}</div>
            <span class="user-name">${user.user}</span>
          </div>
        </td>
        <td class="xp-cell">
          <span class="xp-value">${formattedTotalXP}</span>
          <span class="xp-unit">MB</span>
        </td>
        <td class="progress-cell">
          <div class="progress-bar-container">
            <div class="progress-bar" style="width: ${progressPercentage}%"></div>
          </div>
          <span class="progress-text">${progressPercentage}%</span>
        </td>
      </tr>
    `;
  }

  // Close the table
  tableHtml += `
        </tbody>
      </table>
    </div>
  `;

  return tableHtml;
}


// Function to create SVGs for skills
function createSkillsSVGs(skills) {
  if (!skills || skills.length === 0) {
    console.error("Skills array is empty or undefined");
    return '<div id="svg-skills"><h3>🛠️ Skills Overview</h3><p>No skills data available</p></div>';
  }

  // Sort skills by amount (highest first) and take top 8 for compact display
  const topSkills = skills.sort((a, b) => b.amount - a.amount).slice(0, 8);
  const maxSkillAmount = Math.max(...topSkills.map(skill => skill.amount));

  // Modern color palette for skills
  const skillColors = [
    '#4f46e5', '#06b6d4', '#10b981', '#f59e0b',
    '#ef4444', '#8b5cf6', '#ec4899', '#14b8a6'
  ];

  function getSkillColor(index) {
    return skillColors[index % skillColors.length];
  }

  // Create a compact grid-based skills display
  const skillsMarkup = `
    <div id="svg-skills">
      <h3>🛠️ Top Skills</h3>
      <div class="skills-grid">
        ${topSkills.map((skill, index) => {
          const percentage = Math.round((skill.amount / maxSkillAmount) * 100);
          const color = getSkillColor(index);

          return `
            <div class="skill-card" style="--skill-color: ${color}">
              <div class="skill-header">
                <span class="skill-name">${skill.skill}</span>
                <span class="skill-percentage">${skill.amount}%</span>
              </div>
              <div class="skill-progress">
                <div class="skill-progress-bar" style="width: ${percentage}%; background: ${color}">
                  <div class="skill-progress-glow"></div>
                </div>
              </div>
            </div>
          `;
        }).join('')}
      </div>
      ${skills.length > 8 ? `
        <div class="skills-footer">
          <a href="#" class="skills-count-link" data-page="skills">+${skills.length - 8} more skills</a>
        </div>
      ` : ''}
    </div>
  `;

  return skillsMarkup;
}

// Function to create the complete Skills page with all skills
function createAllSkillsPage(skills) {
  if (!skills || skills.length === 0) {
    return '<div class="no-skills"><p>No skills data available</p></div>';
  }

  // Sort skills by amount (highest first)
  const sortedSkills = skills.sort((a, b) => b.amount - a.amount);
  const maxSkillAmount = Math.max(...sortedSkills.map(skill => skill.amount));

  // Modern color palette for skills (expanded)
  const skillColors = [
    '#4f46e5', '#06b6d4', '#10b981', '#f59e0b', '#ef4444',
    '#8b5cf6', '#ec4899', '#14b8a6', '#f97316', '#84cc16',
    '#6366f1', '#0ea5e9', '#22c55e', '#eab308', '#dc2626',
    '#7c3aed', '#db2777', '#059669', '#d97706', '#65a30d',
    '#5b21b6', '#be185d', '#047857', '#b45309', '#4d7c0f'
  ];

  function getSkillColor(index) {
    return skillColors[index % skillColors.length];
  }

  // Create responsive grid for all skills
  const allSkillsMarkup = `
    <div class="all-skills-container">
      <div class="skills-stats">
        <div class="stat-card">
          <span class="stat-number">${skills.length}</span>
          <span class="stat-label">Total Skills</span>
        </div>
        <div class="stat-card">
          <span class="stat-number">${sortedSkills.filter(s => s.amount >= 50).length}</span>
          <span class="stat-label">Advanced Skills</span>
        </div>
        <div class="stat-card">
          <span class="stat-number">${Math.round(sortedSkills.reduce((sum, s) => sum + s.amount, 0) / skills.length)}%</span>
          <span class="stat-label">Average Level</span>
        </div>
      </div>

      <div class="all-skills-grid">
        ${sortedSkills.map((skill, index) => {
          const percentage = Math.round((skill.amount / maxSkillAmount) * 100);
          const color = getSkillColor(index);

          return `
            <div class="skill-card-full" style="--skill-color: ${color}">
              <div class="skill-header-full">
                <span class="skill-name-full">${skill.skill}</span>
                <span class="skill-percentage-full">${skill.amount}%</span>
              </div>
              <div class="skill-progress-full">
                <div class="skill-progress-bar-full" style="width: ${percentage}%; background: ${color}">
                  <div class="skill-progress-glow-full"></div>
                </div>
              </div>
              <div class="skill-level">
                ${skill.amount >= 80 ? 'Expert' : skill.amount >= 60 ? 'Advanced' : skill.amount >= 40 ? 'Intermediate' : skill.amount >= 20 ? 'Beginner' : 'Novice'}
              </div>
            </div>
          `;
        }).join('')}
      </div>
    </div>
  `;

  return allSkillsMarkup;
}

// Function to create skills as rows for the dashboard
function createSkillsRowsMarkup(skills) {
  if (!skills || skills.length === 0) {
    return '<div class="no-skills"><p>No skills data available</p></div>';
  }

  // Sort skills by amount (highest first) and take top 8
  const topSkills = skills.sort((a, b) => b.amount - a.amount).slice(0, 8);
  const maxSkillAmount = Math.max(...topSkills.map(skill => skill.amount));

  // Modern color palette for skills
  const skillColors = [
    '#4f46e5', '#06b6d4', '#10b981', '#f59e0b', '#ef4444',
    '#8b5cf6', '#ec4899', '#14b8a6'
  ];

  function getSkillColor(index) {
    return skillColors[index % skillColors.length];
  }

  // Create skills rows markup
  const skillsRowsMarkup = `
    <div class="skills-rows-container">
      ${topSkills.map((skill, index) => {
        const percentage = Math.round((skill.amount / maxSkillAmount) * 100);
        const color = getSkillColor(index);

        return `
          <div class="skill-row" style="--skill-color: ${color}">
            <div class="skill-row-info">
              <span class="skill-row-name">${skill.skill}</span>
              <span class="skill-row-percentage">${skill.amount}%</span>
            </div>
            <div class="skill-row-progress">
              <div class="skill-row-progress-bar" style="width: ${percentage}%; background: ${color}">
                <div class="skill-row-progress-glow"></div>
              </div>
            </div>
          </div>
        `;
      }).join('')}

      ${skills.length > 8 ? `
        <div class="skills-footer">
          <a href="#" class="skills-count-link" data-page="skills">+${skills.length - 8} more skills</a>
        </div>
      ` : ''}
    </div>
  `;

  return skillsRowsMarkup;
}




//**************************** login/logout ****************************/

// Function to handle logout
export async function logout() {
  const data = {
      "headers": {
          "x-jwt-token": sessionStorage.getItem('jwt') || '',
      },
      "method": 'GET',
  }

  await fetch(`https://learn.reboot01.com/api/auth/expire`, data);

  data.method = 'POST';
  await fetch(`https://learn.reboot01.com/api/auth/signout`, data);
  sessionStorage.removeItem("jwt");
  renderLoginPage();
}

// Function to check if the user is logged in
function checkLoginStatus() {
  const jwt = sessionStorage.getItem("jwt");
  if (jwt) {
    renderChartsPage(jwt);
  } else {
    renderLoginPage();
  }
}

// Initial render
checkLoginStatus();
