export async function GetJWT(usernameOrEmail, password) {
  const signinEndpoint = "https://learn.reboot01.com/api/auth/signin";

  // Encode the username/email and password as base64
  const encodedCredentials = btoa(`${usernameOrEmail}:${password}`);

  // Make a POST request to the signin endpoint with the encoded credentials in the Authorization header
  const response = await fetch(signinEndpoint, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Basic ${encodedCredentials}`,
    },
  });

  // If the response is not successful, throw an error with the status text and let the user know
  if (!response.ok) {
    alert(
      "Failed to obtain JWT, please check your username/email and password"
    );
    throw new Error("Failed to obtain JWT");
  }

  // Parse the response as JSON
  const jsonResponse = await response.json();

  return jsonResponse;
}
