// Function to fetch user info using GraphQL query and JWT token
export async function GetUserID(jwt) {
  const query = `
      query GetUserInfo {
        user {
          id
          login
        }
      }`;

  try {
    const respData = await fetchInfo(query, jwt);
    console.log("%crespDataforUserID:", "color: blue", respData);

    // Extracting id and login from respData and returning them
    return {
      userID: respData.data.user[0].id,
      userName: respData.data.user[0].login,
    };
  } catch (error) {
    console.error("Error:", error);
    return null; // Return null instead of an empty string in case of error
  }
}

// Function to fetch user info using GraphQL query and JWT token
export async function GetUserInfo(userName, jwt) {
  const query = `
      query GetUserInfo {
          user(where: { login: { _eq: "${userName}" } }) {
              campus
              createdAt
              updatedAt
              email
              firstName
              lastName
              totalDown
              totalUp
              auditRatio
          }
      }`;

  try {
    const respData = await fetchInfo(query, jwt);
    console.log("%crespDataforUserInfo:", "color: blue", respData);

    // Extracting id and login from respData and returning them
    return { userInfo: respData.data.user[0] };
  } catch (error) {
    console.error("Error:", error);
    return null; // Return null instead of an empty string in case of error
  }
}

// Function to get the audit status of the user
export async function GetAuditStatus(jwt) {
  const query = `
      query GetAuditStatus {
        user {
          audits {
              private {
                  audit {
                      grade
                  }
              }
          }
      }
      }`;

  try {
    const respData = await fetchInfo(query, jwt);
    console.log("%crespDataforAuditStatus:", "color: blue", respData);

    // Extracting all grades from respData and returning them as an array
    const audits = respData.data.user[0].audits;
    const grades = audits.map((audit) => audit.private.audit.grade);
    // Count the number of "pass" and "fail" grades and null grades
    let passCount = 0;
    let failCount = 0;
    let nullCount = 0;
    grades.forEach((grade) => {
      if (grade === null) {
        nullCount++;
      } else if (grade >= 1) {
        passCount++;
      } else {
        failCount++;
      }
    });

    return { passCount, failCount, nullCount };
  } catch (error) {
    console.error("Error:", error);
    return null; // Return null instead of an empty string in case of error
  }
}

// Function to get all users gained xps
export async function GetUsersXP(jwt) {
  const query = `query GetUsersXP {
    event(
        where: {
            progresses: {
                event: { xps: { amount: { _gt: "0" } } }
            }
        }
    ) {
        progresses {
            event {
                xps {
                    amount
                    path
                }
            }
        }
    }
}`;

  try {
    const respData = await fetchInfo(query, jwt);
    console.log("%crespDataforUsersXP:", "color: blue", respData);

    // Ensure the structure of the response matches our expectations
    if (!respData || !respData.data || !Array.isArray(respData.data.event)) {
      throw new Error("Unexpected response structure");
    }

    // Extracting all xps from respData and returning them as an array
    const events = respData.data.event;
    const usersXP = events.flatMap((event) =>
      event.progresses.flatMap((progress) =>
        progress.event.xps.map((xp) => {
          return { project: xp.path.split("/").pop(), xp: xp.amount };
        })
      )
    );

    // console.log("%cusersXP:", "color: green", usersXP); // Debugging output

    return { usersXP };
  } catch (error) {
    console.error("Error:", error);
    return null; // Return null instead of an empty string in case of error
  }
}

// Function to get comprehensive project XP mapping for accurate ranking calculations
export async function GetProjectXPMapping(eventId, jwt) {
  const query = `
    query Object($eventId: Int!) {
        object(
            where: {
                type: { _eq: "module" }
                events: { id: { _eq: $eventId } }
            }
        ) {
            type
            name
            childrenRelation {
                attrs
                key
                paths {
                    object {
                        name
                    }
                }
            }
        }
    }`;

  const variables = {
    eventId: eventId,
  };

  try {
    const respData = await fetchInfo(query, jwt, variables);
    console.log("%crespDataforProjectXPMapping:", "color: blue", respData);

    if (!respData || !respData.data || !Array.isArray(respData.data.object)) {
      throw new Error("Unexpected response structure for project XP mapping");
    }

    // Create project name to XP mapping
    const projectXPMapping = {};

    respData.data.object.forEach((obj) => {
      if (obj.childrenRelation && Array.isArray(obj.childrenRelation)) {
        obj.childrenRelation.forEach((relation) => {
          if (relation.attrs && relation.paths && relation.paths.length > 0) {
            const projectName = relation.paths[0].object.name;
            const baseXp = relation.attrs.baseXp;

            if (projectName && baseXp) {
              // Convert XP from base units to the format used in the application (divide by 1000)
              projectXPMapping[projectName] = baseXp / 1000;
            }
          }
        });
      }
    });

    console.log("%cprojectXPMapping:", "color: green", projectXPMapping);
    return { projectXPMapping };
  } catch (error) {
    console.error("Error fetching project XP mapping:", error);
    return { projectXPMapping: {} }; // Return empty mapping in case of error
  }
}

// Function to get the projects status of the user
export async function GetProjectsStatus(userName, eventId, usersXP, jwt) {
  const query = `
      query GetProjectStatus {
          group(
              where: { members: { user: { login: { _eq: "${userName}" } } }, eventId: { _eq: ${eventId} } }
          ) {
              path
              status
              pathByPath {
                  transactions(limit: 1) {
                      amount
                  }
              }
              createdAt
          }      
      }`;

  try {
    const respData = await fetchInfo(query, jwt);
    console.log("%crespDataforProjectStatus:", "color: blue", respData);

    // Initialize counters and arrays
    const statusCounts = { finished: 0, working: 0, setup: 0 };
    const projects = [];

    // Process each group
    respData.data.group.forEach((group) => {
      // Count the status
      if (statusCounts[group.status] !== undefined) {
        statusCounts[group.status]++;
      } else {
        statusCounts[group.status] = 1; // handle unexpected statuses
      }

      // Extract project name from path
      const projectName = group.path.split("/").pop();

      // // Get XP from the first transaction amount
      // const xp = Math.floor(group.pathByPath.transactions[0]?.amount/1000 || 0);

      // Get XP from the usersXP array
      let xp = 0;
      usersXP.forEach((userXP) => {
        if (userXP.project === projectName) {
          xp = userXP.xp / 1000;
        }
      });
      // Save project details
      projects.push({
        name: projectName,
        status: group.status,
        xp: xp,
        createdAt: new Date(group.createdAt),
      });
    });

    // Sort projects by createdAt date
    projects.sort((a, b) => a.createdAt - b.createdAt);

    return { statusCounts, projects };
  } catch (error) {
    console.error("Error:", error);
    return null; // Return null instead of an empty string in case of error
  }
}

// Function to get the eventId of the user
export async function GetEventId(jwt, userName) {
  const query = `
  query GetEventId {
    group(
        where: {
            path: { _eq: "/bahrain/bh-module/go-reloaded" }
            captainLogin: { _eq: "${userName}" }
        }
        limit: 1
    ) {
        eventId
    }
}`;

  try {
    const respData = await fetchInfo(query, jwt);
    console.log("%crespDataforEventId:", "color: blue", respData);

    // Extracting eventId from respData and returning it
    return { eventId: respData.data.group[0].eventId };
  } catch (error) {
    console.error("Error:", error);
    return null; // Return null instead of an empty string in case of error
  }
}

// Function to get all the users ids in the same cohort
export async function GetCohortUsers(jwt, eventId) {
  const query = `
      query GetCohortUsers {
        event(where: { id: { _eq: ${eventId} } }) {
          users {
              login
          }
        }
      }`;

  try {
    const respData = await fetchInfo(query, jwt);
    console.log("%crespDataforCohortUsers:", "color: blue", respData);

    if (!respData.data || !respData.data.event) {
      console.error("Invalid response structure:", respData);
      throw new Error("Invalid response structure");
    }

    // Extracting all users login into an array of usernames
    const users = respData.data.event[0]?.users.map((user) => user.login) || [];
    console.log("%cusers:", "color: green", users); // Debugging output
    return { users };
  } catch (error) {
    console.error("Error:", error);
    return { users: [] }; // Return empty array in case of error
  }
}

// Function to get the users gained xps using the same method as personal XP display for consistency
export async function UsersByXP(jwt, eventId, users, projectXPMapping) {
  const usersByXP = await Promise.all(
    users.map(async (user) => {
      // Use the same query as getUserXpAndLevel for consistency
      const query = `
        query rootEventDetails($userLogin: String!, $rootEventId: Int!) {
          xp: transaction_aggregate(
            where: {
              userLogin: { _eq: $userLogin }
              type: { _eq: "xp" }
              eventId: { _eq: $rootEventId }
            }
          ) { aggregate { sum { amount } } }
        }
      `;

      const variables = {
        userLogin: user,
        rootEventId: eventId,
      };

      try {
        const respData = await fetchInfo(query, jwt, variables);

        if (!respData.data || !respData.data.xp) {
          console.error("Invalid response structure:", respData);
          throw new Error("Invalid response structure");
        }

        // Get total XP from transaction aggregate (same as personal XP display)
        const totalXPRaw = respData.data.xp.aggregate.sum.amount || 0;
        // Convert to the same units as used in the ranking table (divide by 1000 instead of 1000000)
        const totalXP = totalXPRaw / 1000;

        console.log(
          "%cusername: %c%s %ctotalXP: %c%d",
          "color: blue",
          "",
          user,
          "color: blue",
          "",
          totalXP
        );

        // keep the user and its total xp
        return { user, totalXP };
      } catch (error) {
        console.error("Error:", error);
        return { user, totalXP: 0 }; // Return zero XP in case of error
      }
    })
  );

  // Sort the users by totalXP
  usersByXP.sort((a, b) => b.totalXP - a.totalXP);

  return { usersByXP };
}

// Function to fetch user's XP and level using GraphQL query and JWT token
export async function getUserXpAndLevel(userName, eventId, jwt) {
  const query = `
    query rootEventDetails($userLogin: String!, $rootEventId: Int!) {
      xp: transaction_aggregate(
        where: {
          userLogin: { _eq: $userLogin }
          type: { _eq: "xp" }
          eventId: { _eq: $rootEventId }
        }
      ) { aggregate { sum { amount } } }
      level: transaction(
        limit: 1
        order_by: { amount: desc }
        where: {
          userLogin: { _eq: $userLogin }
          type: { _eq: "level" }
          eventId: { _eq: $rootEventId }
        }
      ) { amount }
    }
  `;

  const variables = {
    userLogin: userName,
    rootEventId: eventId,
  };

  try {
    const respData = await fetchInfo(query, jwt, variables);
    console.log("%crespDataforXpAndLevel:", "color: blue", respData);

    const xp = respData.data.xp.aggregate.sum.amount || 0;
    const level = respData.data.level[0].amount || 0;

    return { xp, level };
  } catch (error) {
    console.error("Error:", error);
    return null; // Return null in case of an error
  }
}

// Function to fetch user's skills using GraphQL query and JWT token
export async function getUserSkills(userID, jwt) {
  const query = `
    query user($userId: Int!) {
      user(where: { id: { _eq: $userId } }) {
        transactions(
          order_by: [{ type: desc }, { amount: desc }]
          distinct_on: [type]
          where: { 
            userId: { _eq: $userId }
            type: { _like: "skill_%" }
          }
        ) { 
          type
          amount
        }
      }
    }
  `;

  const variables = {
    userId: userID,
  };

  try {
    const respData = await fetchInfo(query, jwt, variables);
    console.log("%crespDataforUserSkills:", "color: blue", respData);

    const skills = respData.data.user[0].transactions.map((transaction) => ({
      skill: transaction.type.replace("skill_", ""),
      amount: transaction.amount,
    }));
    console.log("%crespDataforUserSkills:", "color: green", skills);
    // sort skills by amount
    skills.sort((a, b) => b.amount - a.amount);

    return skills;
  } catch (error) {
    console.error("Error:", error);
    return null; // Return null in case of an error
  }
}

// Function to fetch data from GraphQL endpoint
async function fetchInfo(query, jwt, variables = {}) {
  const url = "https://learn.reboot01.com/api/graphql-engine/v1/graphql";

  const requestBody = {
    query: query,
    variables: variables, // Include variables in the request body if provided
  };

  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${jwt}`,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    throw new Error(`HTTP error: ${response.status}`);
  }

  const respData = await response.json();

  return respData;
}
