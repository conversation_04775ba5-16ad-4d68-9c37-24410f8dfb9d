// {
//     user {
//       id
//       login
//     }
//   }

// subscription User_by_pk {
//     user_by_pk(id: 476) {
//         id
//         campus
//         createdAt
//         updatedAt
//         email
//         firstName
//         lastName
//         login
//         auditRatio
//         totalDown
//         totalUp
//     }
// }

// query Object {
//     object {
//         authorId
//         campus
//         createdAt
//         id
//         name
//         type
//         updatedAt
//         events {
//             createdAt
//             endAt
//             id
//             objectId
//             parentId
//             path
//             registrationId
//             status
//         }
//         groups {
//             captainId
//             captainLogin
//             createdAt
//             eventId
//             id
//             objectId
//             status
//             updatedAt
//         }
//     }
// }

// query Group {
//     group(
//         where: { members: { userLogin: { _eq: "amali" } }, eventId: { _eq: 20 } }
//     ) {
//         captainId
//         captainLogin
//         createdAt
//         eventId
//         id
//         objectId
//         path
//         status
//         updatedAt
//         members {
//             user {
//                 login
//             }
//         }
//     }
// }


// query:{
//     user{
//       audits{
//         private{
//           audit{
//             grade
//             group{
//               members{
//                 userLogin
//               }
//             }
//           }     
//         }
//       }
//     }
//   }

//   query User {
//     user {
//         audits {
//             private {
//                 audit {
//                     grade
//                     group {
//                         captainLogin
//                         path
//                         status
//                         members {
//                             userLogin
//                         }
//                     }
//                 }
//             }
//         }
//     }
// }


// query Group {
//   group(
//       where: { members: { user: { login: { _eq: "amali" } } }, eventId: { _eq: 20 } }
//   ) {
//       eventId
//       path
//       status
//       members {
//           user {
//               login
//           }
//       }
//       pathByPath {
//           transactions(limit: 1) {
//               amount
//           }
//       }
//   }
// }
