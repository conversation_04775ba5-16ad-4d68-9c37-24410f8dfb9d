<p align="center">
    <img src="./static/logo1.png" alt="logo"  width="700px"  />
</p>


<h2 align="">About The Project</h2>

<p>
Welcome to My GraphQL Profile Page Project!

This project involves creating a personalized profile page using JavaScript and custom GraphQL queries to fetch and display user data. Authentication is handled with Gita login and JWT for secure API access. The profile page showcases personalized data and interactive statistics (XP, grades, etc.) using SVG and is hosted on GitHub Pages.

It was a great experience to:

- Build a dynamic profile page using GraphQL & SVG.
- Fetch school data with custom GraphQL queries.
- Learn more about GraphQL, JWT, SVG, and UI/UX.
</p>

## Table of Contents

- [Getting Started](#getting-started)
- [Usage](#usage)
- [Directory Structure](#directory-structure)
- [Screenshots](#screenshots)
- [Author](#author)

## Getting Started
You can run the graphql project with the following command:
```console
git clone https://github.com/amali01/graphql.git
cd graphql
```

## Usage

1. Open https://amali01.github.io/graphql/ in a browser .
2. login with your reboot account .

### Directory Structure
```console
─ graphql/
│
├── static/
│   ├── favicon.ico
│   ├── logo.png
|   ├── style.css
|   └── ...
|
├── app.js/
│
├── auth.js/
│
├── index.html/
│
├── myQuery.js/
|
├── README.md
└── ...
```

## Screenshots
### Login pages
<p>
    <img src="./static/login.png" alt="Login" hight="700px" width="800px" />
</p>

### Status Page
<p>
    <img src="./static/status.png" alt="Home Page" hight="700px" width="800px"  />
</p>

## Author
- [amali01](https://github.com/amali01)