html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  background-image: linear-gradient(116deg, rgba(232, 232, 232, 0.03) 0%, rgba(232, 232, 232, 0.03) 10%,rgba(14, 14, 14, 0.03) 10%, rgba(14, 14, 14, 0.03) 66%,rgba(232, 232, 232, 0.03) 66%, rgba(232, 232, 232, 0.03) 72%,rgba(44, 44, 44, 0.03) 72%, rgba(44, 44, 44, 0.03) 81%,rgba(51, 51, 51, 0.03) 81%, rgba(51, 51, 51, 0.03) 100%),linear-gradient(109deg, rgba(155, 155, 155, 0.03) 0%, rgba(155, 155, 155, 0.03) 23%,rgba(30, 30, 30, 0.03) 23%, rgba(30, 30, 30, 0.03) 63%,rgba(124, 124, 124, 0.03) 63%, rgba(124, 124, 124, 0.03) 73%,rgba(195, 195, 195, 0.03) 73%, rgba(195, 195, 195, 0.03) 84%,rgba(187, 187, 187, 0.03) 84%, rgba(187, 187, 187, 0.03) 100%),linear-gradient(79deg, rgba(254, 254, 254, 0.03) 0%, rgba(254, 254, 254, 0.03) 27%,rgba(180, 180, 180, 0.03) 27%, rgba(180, 180, 180, 0.03) 33%,rgba(167, 167, 167, 0.03) 33%, rgba(167, 167, 167, 0.03) 34%,rgba(68, 68, 68, 0.03) 34%, rgba(68, 68, 68, 0.03) 63%,rgba(171, 171, 171, 0.03) 63%, rgba(171, 171, 171, 0.03) 100%),linear-gradient(109deg, rgba(71, 71, 71, 0.03) 0%, rgba(71, 71, 71, 0.03) 3%,rgba(97, 97, 97, 0.03) 3%, rgba(97, 97, 97, 0.03) 40%,rgba(40, 40, 40, 0.03) 40%, rgba(40, 40, 40, 0.03) 55%,rgba(5, 5, 5, 0.03) 55%, rgba(5, 5, 5, 0.03) 73%,rgba(242, 242, 242, 0.03) 73%, rgba(242, 242, 242, 0.03) 100%),linear-gradient(271deg, rgba(70, 70, 70, 0.03) 0%, rgba(70, 70, 70, 0.03) 11%,rgba(178, 178, 178, 0.03) 11%, rgba(178, 178, 178, 0.03) 23%,rgba(28, 28, 28, 0.03) 23%, rgba(28, 28, 28, 0.03) 72%,rgba(152, 152, 152, 0.03) 72%, rgba(152, 152, 152, 0.03) 86%,rgba(43, 43, 43, 0.03) 86%, rgba(43, 43, 43, 0.03) 100%),linear-gradient(90deg, rgb(27, 27, 27),rgb(1, 1, 1));
  /* background-repeat: no-repeat; */
  background-size: cover;
}

body {
  color: white;
  /* background-color: #272529; */
  display: flex;
  /* justify-content: center; */
  flex-direction: column;
  align-items: center;

  font-family: 'Roboto', sans-serif;
}

#logo{
  max-height: 130px;
  padding: 10px;
  margin: 10px;
}

#logoutButton{
  background-color: #f44336;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px;
  margin: 5px;
  cursor: pointer;
}

#loginButton,#toggleButton1{
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px;
  margin: 10px;
  cursor: pointer;
}
#userCountSelect{
  padding: 5px;
  margin: 5px;
  border-radius: 5px;
  border: none;
}
input{
  padding: 5px;
  margin: 5px;
  border-radius: 5px;
  border: none;
}

#loginForm{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 15px;
  padding: 20px;
  max-width: fit-content;
  min-width: 300px;

  background: rgba(255, 255, 255, 0.08); /* semi-transparent background */
  -webkit-backdrop-filter: blur(10px); /* for Safari */
}


#app{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 15px;
  padding: 20px;
  max-width: fit-content;
  min-width: 300px;

  background: rgba(255, 255, 255, 0.06); /* semi-transparent background */
  backdrop-filter: blur(10px); /* blur effect */
  -webkit-backdrop-filter: blur(10px); /* for Safari */
}

#welcome{
  display: flex;
  justify-content: space-between;
  border-radius: 15px;
  padding: 10px;
  width: 95%;
  /* font-weight: 550; */
  /* background: rgba(255, 255, 255, 0.08); */
  /* backdrop-filter: blur(10px); */
  /* -webkit-backdrop-filter: blur(10px); */
  align-items: center; /* for Safari */
}

#chartsPage{
  display: flex;
  flex-direction: row;
  justify-content: center;
  /* align-items: center; */
  border-radius: 15px;
  padding: 5px;
  margin: 5px;
  max-width: fit-content;
  min-width: 300px;
}

#RightSide{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 15px;
  padding: 20px;
  /* margin: 10px; */
  max-width: fit-content;
  min-width: 300px;
  -webkit-backdrop-filter: blur(10px); /* for Safari */
}

#userInfo {
  display: flex;
  flex-direction: column;
  justify-content: center;
  /* align-items: center; */

  border-radius: 15px;
  /* margin: 5px; */
  padding: 10px;
  max-width: fit-content;
  min-width: 300px;

  -webkit-backdrop-filter: blur(10px); /* for Safari */
}


#svg {
  max-width: fit-content;
  min-width: 300px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 15px;
  /* margin: 5px; */
  padding: 10px;

  -webkit-backdrop-filter: blur(10px); /* for Safari */

}
#svg-pie{
  max-width: fit-content;
  display: flex;
  margin: 5px;
  padding: 10px;
}

#svg-donut{
  max-width: fit-content;
  display: flex;
  width: 100%;
  min-width: 300px;
  border-radius: 15px;
  margin: 5px;
  padding: 10px;

  -webkit-backdrop-filter: blur(10px); /* for Safari */

}

#svg-skills{
  max-width: fit-content;
  display: flex;
  width: 100%;
  min-width: 300px;
  border-radius: 15px;
  margin: 5px;
  padding: 10px;

  -webkit-backdrop-filter: blur(10px); /* for Safari */}

#projectsTable,#usersByXP {
  border-collapse: collapse;
  width: 100%;
  border-radius: 15px;
  padding: 10px;
  max-width: fit-content;
  /* min-width: 300px; */

  background: rgba(255, 255, 255, 0.08); /* semi-transparent background */
  backdrop-filter: blur(10px); /* blur effect */
  -webkit-backdrop-filter: blur(10px); /* for Safari */
}

#projectsTable th ,#usersByXP th{
  background-color: #f2f2f2;
  color: black;
}

#projectsTable th, #projectsTable td ,#usersByXP th, #usersByXP td{
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

#toggleButton{
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px;
  margin: 10px;
  cursor: pointer;
}