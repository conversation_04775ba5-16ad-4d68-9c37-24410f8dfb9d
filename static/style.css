/* CSS Custom Properties for consistent theming */
:root {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --secondary-color: #06b6d4;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --background-dark: #0f172a;
  --background-card: rgba(255, 255, 255, 0.08);
  --background-card-hover: rgba(255, 255, 255, 0.12);
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --transition: all 0.2s ease-in-out;
}

* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  background-attachment: fixed;
  scroll-behavior: smooth;
}

body {
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  overflow-x: hidden;
}


/* Button Styling */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  min-width: 120px;
  gap: 8px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
}

#logoutButton {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
  color: white;
  margin: 8px;
}

#logoutButton:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

#loginButton, #toggleButton1, #toggleButton {
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: white;
  margin: 8px;
}

#loginButton:hover, #toggleButton1:hover, #toggleButton:hover {
  background: linear-gradient(135deg, #059669, #047857);
}

/* Form Elements */
#userCountSelect, input {
  padding: 12px 16px;
  margin: 8px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--background-card);
  color: var(--text-primary);
  font-size: 14px;
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

#userCountSelect:focus, input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

#userCountSelect:hover, input:hover {
  border-color: var(--primary-hover);
  background: var(--background-card-hover);
}

/* Login Form */
#loginForm {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: var(--border-radius-lg);
  padding: 40px;
  max-width: 400px;
  width: 100%;
  background: var(--background-card);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
  animation: fadeInUp 0.6s ease-out;
}

/* Main App Container with Sidebar */
#app {
  display: flex;
  min-height: 100vh;
  width: 100%;
  animation: fadeIn 0.8s ease-out;
}

/* Sidebar Navigation */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, var(--background-card), rgba(255, 255, 255, 0.05));
  border-right: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: var(--transition);
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid var(--border-color);
  text-align: center;
}

.sidebar-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  margin: 4px 12px;
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  text-decoration: none;
  transition: var(--transition);
  cursor: pointer;
  gap: 12px;
  font-weight: 500;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.08);
  color: var(--text-primary);
  transform: translateX(4px);
}

.nav-item.active {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-item svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

/* Main Content Area */
.main-content {
  flex: 1;
  margin-left: 280px;
  padding: 24px;
  overflow-y: auto;
  max-height: 100vh;
}

/* Page Container */
.page {
  display: none;
  max-width: 1200px;
  margin: 0 auto;
  animation: fadeInUp 0.6s ease-out;
}

.page.active {
  display: block;
}

/* Page Titles */
.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 32px 0;
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Welcome Header */
#welcome {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 24px 32px;
  background: linear-gradient(135deg, var(--background-card), rgba(255, 255, 255, 0.05));
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
  animation: slideIn 0.6s ease-out;
  margin-bottom: 24px;
}

#welcome p {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

#welcome p:first-child {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12px;
}

.welcome-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.ranking-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  justify-content: center;
}

/* Dashboard Layout */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  width: 100%;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

/* Leaderboard Tabs */
.leaderboard-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  justify-content: center;
}

.tab-button {
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  background: var(--background-card);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  border: 1px solid var(--border-color);
}

.tab-button:hover {
  background: var(--background-card-hover);
  color: var(--text-primary);
}

.tab-button.active {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  border-color: var(--primary-color);
}

/* Mobile Sidebar */
.sidebar-toggle {
  display: none;
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1001;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 12px;
  cursor: pointer;
  box-shadow: var(--shadow-md);
}

@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .sidebar-toggle {
    display: block;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

/* Left and Right Side Containers */
#leftSide, #RightSide {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 0; /* Allow flex items to shrink */
}

#RightSide {
  align-items: center;
}

/* User Info Card */
#userInfo {
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  padding: 24px;
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  width: 100%;
}

#userInfo:hover {
  background: var(--background-card-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

#userInfo p {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 12px;
}

#userInfo div {
  margin: 8px 0;
  padding: 12px 16px;
  font-size: 14px;
  color: var(--text-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  transition: var(--transition);
}

#userInfo div:hover {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
}

#userInfo div:last-child {
  border-bottom: none;
}

#userInfo div span:first-child {
  font-weight: 500;
  color: var(--text-muted);
  min-width: 100px;
}

#userInfo div span:last-child {
  font-weight: 600;
  color: var(--text-primary);
  text-align: right;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Special handling for email field */
.email-field {
  word-break: break-all;
  white-space: normal !important;
  line-height: 1.3;
  max-width: 180px !important;
  cursor: help;
}

.email-field:hover {
  color: var(--primary-color) !important;
}

/* SVG and Chart Containers */
#svg {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  width: 100%;
  min-height: 120px;
}

#svg:hover {
  background: var(--background-card-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

#svg-pie, #svg-donut, #svg-skills {
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  padding: 24px;
  margin: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

#svg-pie:hover, #svg-donut:hover, #svg-skills:hover {
  background: var(--background-card-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Chart Titles */
#svg-pie h3, #svg-donut h3, #svg-skills h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
}

/* Skills Grid Layout */
.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.skill-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 16px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.skill-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--skill-color, var(--primary-color));
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.skill-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
  text-transform: capitalize;
}

.skill-percentage {
  font-weight: 700;
  color: var(--skill-color, var(--primary-color));
  font-size: 14px;
}

.skill-progress {
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  position: relative;
}

.skill-progress-bar {
  height: 100%;
  border-radius: 3px;
  position: relative;
  transition: width 1s ease-out;
  animation: skillBarGrow 1s ease-out;
}

.skill-progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4));
  animation: skillGlow 2s ease-in-out infinite;
}

.skills-footer {
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid var(--border-color);
}

.skills-count-link {
  color: var(--primary-color);
  font-size: 12px;
  font-style: italic;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  border-bottom: 1px dotted var(--primary-color);
}

.skills-count-link:hover {
  color: var(--primary-hover);
  border-bottom-color: var(--primary-hover);
}

@keyframes skillBarGrow {
  from { width: 0; }
  to { width: var(--final-width, 100%); }
}

@keyframes skillGlow {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* Enhanced Project Table */
.table-container {
  overflow-x: auto;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
}

.projects-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.projects-table thead th {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  font-weight: 600;
  padding: 20px 16px;
  text-align: left;
  font-size: 14px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  border: none;
}

.projects-table tbody td {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: 14px;
  vertical-align: middle;
}

.project-row:hover {
  background: rgba(255, 255, 255, 0.05);
}

.project-row:hover td {
  color: var(--text-primary);
}

.project-number {
  font-weight: 600;
  color: var(--text-muted);
  width: 60px;
}

.project-name-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.project-title {
  font-weight: 600;
  color: var(--text-primary);
  text-transform: capitalize;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.project-xp {
  text-align: right;
}

.xp-value {
  font-weight: 700;
  color: var(--success-color);
  font-size: 16px;
}

.xp-unit {
  color: var(--text-muted);
  font-size: 12px;
  margin-left: 4px;
}

.project-date {
  color: var(--text-muted);
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
}

/* Enhanced Leaderboard Table */
.leaderboard-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.leaderboard-table thead th {
  background: linear-gradient(135deg, var(--secondary-color), #0891b2);
  color: white;
  font-weight: 600;
  padding: 20px 16px;
  text-align: left;
  font-size: 14px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  border: none;
}

.leaderboard-table tbody td {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: 14px;
  vertical-align: middle;
}

.leaderboard-row:hover {
  background: rgba(255, 255, 255, 0.05);
}

.leaderboard-row.top-rank {
  background: linear-gradient(90deg, rgba(255, 215, 0, 0.1), transparent);
}

.leaderboard-row.high-rank {
  background: linear-gradient(90deg, rgba(192, 192, 192, 0.1), transparent);
}

.rank-cell {
  width: 80px;
  text-align: center;
}

.rank-badge {
  font-size: 18px;
  font-weight: 700;
}

.user-cell {
  min-width: 200px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 16px;
}

.user-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
}

.xp-cell {
  text-align: right;
  min-width: 120px;
}

.xp-value {
  font-weight: 700;
  color: var(--success-color);
  font-size: 18px;
}

.xp-unit {
  color: var(--text-muted);
  font-size: 12px;
  margin-left: 4px;
}

.progress-cell {
  min-width: 150px;
}

.progress-bar-container {
  width: 100px;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color), var(--secondary-color));
  border-radius: 4px;
  transition: width 1s ease-out;
}

.progress-text {
  font-size: 12px;
  color: var(--text-muted);
}

/* All Skills Page */
.all-skills-container {
  max-width: 1200px;
  margin: 0 auto;
}

.skills-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: var(--background-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 24px;
  text-align: center;
  transition: var(--transition);
}

.stat-card:hover {
  background: var(--background-card-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-number {
  display: block;
  font-size: 32px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Skills Grid */
.all-skills-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* Desktop: 4 columns */
@media (min-width: 1200px) {
  .all-skills-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Large tablet: 3 columns */
@media (min-width: 900px) and (max-width: 1199px) {
  .all-skills-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Tablet: 2 columns */
@media (min-width: 600px) and (max-width: 899px) {
  .all-skills-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Mobile: 1-2 columns */
@media (max-width: 599px) {
  .all-skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }
}

.skill-card-full {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 20px;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.skill-card-full:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--skill-color, var(--primary-color));
}

.skill-header-full {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.skill-name-full {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
  text-transform: capitalize;
}

.skill-percentage-full {
  font-weight: 700;
  color: var(--skill-color, var(--primary-color));
  font-size: 18px;
}

.skill-progress-full {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin-bottom: 12px;
}

.skill-progress-bar-full {
  height: 100%;
  border-radius: 4px;
  position: relative;
  transition: width 1s ease-out;
  animation: skillBarGrowFull 1.5s ease-out;
}

.skill-progress-glow-full {
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4));
  animation: skillGlowFull 2s ease-in-out infinite;
}

.skill-level {
  text-align: center;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 1px;
}

@keyframes skillBarGrowFull {
  from { width: 0; }
  to { width: var(--final-width, 100%); }
}

@keyframes skillGlowFull {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

.no-skills {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-muted);
}

/* Chart Titles */
.chart-title {
  font-size: 18px;
  font-weight: 700;
  margin: 0 0 20px 0;
  color: var(--text-primary);
  text-align: center;
}

/* Redesigned Audit Statistics */
#audit-statistics {
  background: var(--background-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 24px;
  box-shadow: var(--shadow-md);
}

.audit-ratio-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(6, 182, 212, 0.1));
  border-radius: var(--border-radius);
  margin-bottom: 24px;
  border: 1px solid rgba(79, 70, 229, 0.2);
}

.audit-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
}

.audit-value {
  font-weight: 700;
  font-size: 20px;
  color: var(--primary-color);
}

.audit-bars-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.audit-bar-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.audit-bar-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.bar-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.bar-value {
  font-weight: 700;
  color: var(--text-secondary);
  font-size: 14px;
}

.audit-progress-container {
  position: relative;
  height: 12px;
  border-radius: 6px;
  overflow: hidden;
}

.audit-progress-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

.audit-progress-bar {
  position: relative;
  height: 100%;
  border-radius: 6px;
  transition: width 1.5s ease-out;
  animation: auditBarGrow 1.5s ease-out;
}

.done-bar {
  background: linear-gradient(90deg, #10b981, #059669);
}

.received-bar {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.audit-progress-glow {
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4));
  animation: auditGlow 2s ease-in-out infinite;
}

@keyframes auditBarGrow {
  from { width: 0; }
  to { width: var(--final-width, 100%); }
}

@keyframes auditGlow {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

/* Audit Status Pie Chart */
#audit-status-chart {
  background: var(--background-card);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 24px;
  box-shadow: var(--shadow-md);
}

.pie-chart-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

#svg-pie {
  flex-shrink: 0;
}

.pie-slice {
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.pie-slice:hover {
  opacity: 0.8;
}

.pie-legend {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  background: rgba(255, 255, 255, 0.02);
  transition: var(--transition);
}

.legend-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  flex-shrink: 0;
}

.legend-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.legend-label {
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 14px;
}

.legend-value {
  font-weight: 700;
  color: var(--text-primary);
  font-size: 14px;
}

.legend-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  margin-top: 8px;
  border-top: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
}

.total-label {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 16px;
}

.total-value {
  font-weight: 700;
  color: var(--primary-color);
  font-size: 18px;
}

/* Status Overview Container */
#status-overview {
  background: linear-gradient(135deg, var(--background-card), rgba(255, 255, 255, 0.06));
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 32px;
  box-shadow: var(--shadow-lg);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

#status-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--success-color));
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

#status-overview:hover {
  background: linear-gradient(135deg, var(--background-card-hover), rgba(255, 255, 255, 0.08));
  transform: translateY(-4px);
  box-shadow: 0 20px 40px -8px rgba(0, 0, 0, 0.2);
}

#status-overview .chart-title {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 28px;
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

#status-overview .chart-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 1px;
}

.status-charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  align-items: start;
}

.status-chart-wrapper {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: var(--border-radius);
  padding: 24px;
  transition: var(--transition);
  position: relative;
}

.status-chart-wrapper:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

@media (max-width: 768px) {
  .status-charts-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .pie-chart-container,
  .donut-chart-container {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .pie-legend,
  .donut-legend {
    width: 100%;
  }
  
  #status-overview {
    padding: 24px;
  }
}

/* Status charts adjustments */
#audit-status-chart,
#projects-status-chart {
  background: transparent;
  border: none;
  padding: 0;
  box-shadow: none;
}

#audit-status-chart .chart-title,
#projects-status-chart .chart-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  text-align: center;
  color: var(--text-primary);
  position: relative;
}

#audit-status-chart .chart-title::before,
#projects-status-chart .chart-title::before {
  content: '⚖️';
  margin-right: 8px;
}

#projects-status-chart .chart-title::before {
  content: '📊';
}

/* Projects Status Donut Chart */
#projects-status-chart {
  background: transparent;
  border: none;
  padding: 0;
  box-shadow: none;
}

.donut-chart-container {
  display: flex;
  align-items: center;
  gap: 20px;
}

#svg-donut {
  flex-shrink: 0;
}

.donut-segment {
  cursor: pointer;
  transition: stroke-width 0.3s ease;
}

.donut-segment:hover {
  stroke-width: 25;
}

.donut-legend {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
}

/* Table Containers */
#projectsTable, #usersByXP {
  width: 100%;
  max-width: 1200px;
  margin: 20px 0;
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  padding: 24px;
  animation: fadeInUp 0.6s ease-out;
}

#projectsTable p, #usersByXP p {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Table Styling */
table {
  width: 100%;
  border-collapse: collapse;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  font-weight: 600;
  padding: 16px 20px;
  text-align: left;
  font-size: 14px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

td {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: 14px;
  transition: var(--transition);
}

tr:nth-child(even) {
  background: rgba(255, 255, 255, 0.02);
}

tr:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: scale(1.01);
}

tr:hover td {
  color: var(--text-primary);
}

/* Bottom Section */
#bottom {
  display: flex;
  gap: 16px;
  justify-content: center;
  align-items: center;
  margin: 24px 0;
  flex-wrap: wrap;
}

/* Control Section for Rankings */
#usersByXP label {
  font-weight: 600;
  color: var(--text-primary);
  margin-right: 12px;
}

#usersByXPTable {
  margin-top: 20px;
}

/* Loading States */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 10px;
  }

  #app {
    padding: 10px;
    gap: 16px;
  }

  #welcome {
    flex-direction: column;
    gap: 12px;
    text-align: center;
    padding: 20px;
  }

  #welcome p {
    font-size: 14px;
  }

  #welcome p:first-child {
    font-size: 18px;
  }

  #chartsPage {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  #userInfo {
    padding: 16px;
  }

  #svg-pie, #svg-donut, #svg-skills {
    padding: 16px;
  }

  #projectsTable, #usersByXP {
    padding: 16px;
    margin: 16px 0;
  }

  table {
    font-size: 12px;
  }

  th, td {
    padding: 12px 8px;
  }

  .btn {
    padding: 10px 16px;
    font-size: 12px;
    min-width: 100px;
  }

  #bottom {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  #welcome {
    padding: 16px;
  }

  #userInfo div {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  table {
    font-size: 11px;
  }

  th, td {
    padding: 8px 6px;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles for Accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --background-card: rgba(255, 255, 255, 0.15);
    --border-color: rgba(255, 255, 255, 0.3);
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
  }
}
