/* CSS Custom Properties for consistent theming */
:root {
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --secondary-color: #06b6d4;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --background-dark: #0f172a;
  --background-card: rgba(255, 255, 255, 0.08);
  --background-card-hover: rgba(255, 255, 255, 0.12);
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --transition: all 0.2s ease-in-out;
}

* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  background-attachment: fixed;
  scroll-behavior: smooth;
}

body {
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  overflow-x: hidden;
}

/* Logo Styling */
#logo {
  max-height: 130px;
  padding: 15px;
  margin: 15px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
  transition: var(--transition);
}

#logo:hover {
  transform: scale(1.05);
}

/* Button Styling */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--shadow-sm);
  min-width: 120px;
  gap: 8px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
}

#logoutButton {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
  color: white;
  margin: 8px;
}

#logoutButton:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
}

#loginButton, #toggleButton1, #toggleButton {
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: white;
  margin: 8px;
}

#loginButton:hover, #toggleButton1:hover, #toggleButton:hover {
  background: linear-gradient(135deg, #059669, #047857);
}

/* Form Elements */
#userCountSelect, input {
  padding: 12px 16px;
  margin: 8px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--background-card);
  color: var(--text-primary);
  font-size: 14px;
  transition: var(--transition);
  backdrop-filter: blur(10px);
}

#userCountSelect:focus, input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

#userCountSelect:hover, input:hover {
  border-color: var(--primary-hover);
  background: var(--background-card-hover);
}

/* Login Form */
#loginForm {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: var(--border-radius-lg);
  padding: 40px;
  max-width: 400px;
  width: 100%;
  background: var(--background-card);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-lg);
  animation: fadeInUp 0.6s ease-out;
}

/* Main App Container */
#app {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  gap: 24px;
  animation: fadeIn 0.8s ease-out;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Welcome Header */
#welcome {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 24px 32px;
  background: linear-gradient(135deg, var(--background-card), rgba(255, 255, 255, 0.05));
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
  animation: slideIn 0.6s ease-out;
}

#welcome p {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

#welcome p:first-child {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12px;
}

.welcome-stats {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  transition: var(--transition);
}

.stat-item:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.ranking-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
  justify-content: center;
}

/* Charts Page Layout */
#chartsPage {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  width: 100%;
  max-width: 1200px;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

@media (max-width: 1024px) {
  #chartsPage {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

/* Left and Right Side Containers */
#leftSide, #RightSide {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 0; /* Allow flex items to shrink */
}

#RightSide {
  align-items: center;
}

/* User Info Card */
#userInfo {
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  padding: 24px;
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  width: 100%;
}

#userInfo:hover {
  background: var(--background-card-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

#userInfo p {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  border-bottom: 2px solid var(--border-color);
  padding-bottom: 12px;
}

#userInfo div {
  margin: 12px 0;
  padding: 8px 0;
  font-size: 14px;
  color: var(--text-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

#userInfo div:last-child {
  border-bottom: none;
}

#userInfo div:before {
  content: '';
  width: 4px;
  height: 4px;
  background: var(--primary-color);
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
}

/* SVG and Chart Containers */
#svg {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  width: 100%;
  min-height: 120px;
}

#svg:hover {
  background: var(--background-card-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

#svg-pie, #svg-donut, #svg-skills {
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-md);
  transition: var(--transition);
  padding: 24px;
  margin: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

#svg-pie:hover, #svg-donut:hover, #svg-skills:hover {
  background: var(--background-card-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Chart Titles */
#svg-pie h3, #svg-donut h3, #svg-skills h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
}

/* Skills Section Enhancements */
#svg-skills svg {
  width: 100%;
  max-width: 400px;
}

.skill-bar:hover rect:last-of-type {
  filter: brightness(1.2);
}

.skill-bar text {
  transition: var(--transition);
}

.skill-bar:hover text {
  fill: var(--text-primary) !important;
  font-weight: 600;
}

/* Table Containers */
#projectsTable, #usersByXP {
  width: 100%;
  max-width: 1200px;
  margin: 20px 0;
  background: var(--background-card);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: var(--shadow-lg);
  padding: 24px;
  animation: fadeInUp 0.6s ease-out;
}

#projectsTable p, #usersByXP p {
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  text-align: center;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Table Styling */
table {
  width: 100%;
  border-collapse: collapse;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

th {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  font-weight: 600;
  padding: 16px 20px;
  text-align: left;
  font-size: 14px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

td {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
  font-size: 14px;
  transition: var(--transition);
}

tr:nth-child(even) {
  background: rgba(255, 255, 255, 0.02);
}

tr:hover {
  background: rgba(255, 255, 255, 0.05);
  transform: scale(1.01);
}

tr:hover td {
  color: var(--text-primary);
}

/* Bottom Section */
#bottom {
  display: flex;
  gap: 16px;
  justify-content: center;
  align-items: center;
  margin: 24px 0;
  flex-wrap: wrap;
}

/* Control Section for Rankings */
#usersByXP label {
  font-weight: 600;
  color: var(--text-primary);
  margin-right: 12px;
}

#usersByXPTable {
  margin-top: 20px;
}

/* Loading States */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 10px;
  }

  #app {
    padding: 10px;
    gap: 16px;
  }

  #welcome {
    flex-direction: column;
    gap: 12px;
    text-align: center;
    padding: 20px;
  }

  #welcome p {
    font-size: 14px;
  }

  #welcome p:first-child {
    font-size: 18px;
  }

  #chartsPage {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  #userInfo {
    padding: 16px;
  }

  #svg-pie, #svg-donut, #svg-skills {
    padding: 16px;
  }

  #projectsTable, #usersByXP {
    padding: 16px;
    margin: 16px 0;
  }

  table {
    font-size: 12px;
  }

  th, td {
    padding: 12px 8px;
  }

  .btn {
    padding: 10px 16px;
    font-size: 12px;
    min-width: 100px;
  }

  #bottom {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  #welcome {
    padding: 16px;
  }

  #userInfo div {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  table {
    font-size: 11px;
  }

  th, td {
    padding: 8px 6px;
  }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles for Accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --background-card: rgba(255, 255, 255, 0.15);
    --border-color: rgba(255, 255, 255, 0.3);
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
  }
}